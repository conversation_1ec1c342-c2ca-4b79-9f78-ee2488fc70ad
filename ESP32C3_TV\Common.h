#ifndef COMMON_h
#define COMMON_h

#include "Arduino_GFX_Library.h"
#include "pin_config.h"

// 声明全局的gfx对象，在main程序中定义
extern Arduino_GFX *gfx;
extern Arduino_Canvas *canvas;

// 虚拟画布参数
extern int canvas_x, canvas_y;
#define CANVAS_WIDTH 240
#define CANVAS_HEIGHT 240

// 物理屏幕参数
#define PHYSICAL_SCREEN_WIDTH 240
#define PHYSICAL_SCREEN_ACTUAL_HEIGHT 240

// 70年代复古绿色RGB565颜色定义
#define RETRO_GREEN_70S 0x0460    // 70年代复古绿色
#define BLACK 0x0000              // 黑色背景
#define WHITE RETRO_GREEN_70S     // 将白色替换为复古绿色

#endif
