#include "stock_monitor.h"
#include <esp_heap_caps.h>

// 外部声明
extern HWCDC USBSerial;

// 股票监控模块支持双缓冲系统，使用240x240全屏缓冲区

StockMonitor::StockMonitor() {
    updateInterval = DEFAULT_UPDATE_INTERVAL;
    lastUpdateTime = 0;
    lastError = "";

    // 初始化滚动错误提示
    scrollOffset = 0;
    lastScrollTime = 0;
    hasError = false;

    // 初始化股票专用双缓冲
    stockFrameBuffer = nullptr;
    useStockFrameBuffer = false;

    // 初始化股票数据
    for (int i = 0; i < 4; i++) {
        stocks[i].isValid = false;
        stocks[i].lastUpdate = 0;
        stockCodes[i] = "";
    }

    // 初始化指数数据
    for (int i = 0; i < 2; i++) {
        indices[i].isValid = false;
        indices[i].lastUpdate = 0;
    }

    // 设置指数名称
    indices[0].name = "上证指数";
    indices[1].name = "深证成指";

    // 初始化智能局部刷新缓存
    for (int i = 0; i < 4; i++) {
        lastDisplayedStocks[i].isValid = false;
        lastDisplayedStocks[i].currentPrice = 0.0;
        lastDisplayedStocks[i].changePercent = 0.0;
    }

    for (int i = 0; i < 2; i++) {
        lastDisplayedIndices[i].isValid = false;
        lastDisplayedIndices[i].currentValue = 0.0;
        lastDisplayedIndices[i].changePercent = 0.0;
    }

    lastDisplayedError = "";
    isFirstDisplay = true;
}

StockMonitor::~StockMonitor() {
    cleanup();
}

void StockMonitor::cleanup() {
    // 释放股票专用双缓冲内存
    if (stockFrameBuffer != nullptr) {
        free(stockFrameBuffer);
        stockFrameBuffer = nullptr;
        useStockFrameBuffer = false;
        USBSerial.println("股票监控帧缓冲区已释放");
    }
}

void StockMonitor::init(String codes[4]) {
    USBSerial.println("=== 股票监控模块初始化 ===");

    // 初始化股票专用双缓冲
    if (!initStockFrameBuffer()) {
        USBSerial.println("股票双缓冲初始化失败，使用直接绘制模式");
        useStockFrameBuffer = false;
    } else {
        USBSerial.println("股票双缓冲初始化成功");
        useStockFrameBuffer = true;
    }

    // 设置股票代码
    setStockCodes(codes);

    // 根据市场状态设置更新间隔
    if (isMarketOpen()) {
        updateInterval = MARKET_UPDATE_INTERVAL;
        USBSerial.println("市场开盘中，使用1分钟更新间隔");
    } else {
        updateInterval = AFTER_MARKET_UPDATE_INTERVAL;
        USBSerial.println("市场闭盘中，使用30分钟更新间隔");
    }

    // 初始化完成，等待真实数据更新
    USBSerial.println("等待获取真实股票数据...");

    USBSerial.println("股票监控模块初始化成功");
}

void StockMonitor::setStockCodes(String codes[4]) {
    for (int i = 0; i < 4; i++) {
        stockCodes[i] = codes[i];
        if (codes[i].length() > 0) {
            USBSerial.print("股票");
            USBSerial.print(i + 1);
            USBSerial.print(": ");
            USBSerial.print(codes[i]);
            USBSerial.print(" (");
            USBSerial.print(getExchangeName(codes[i]));
            USBSerial.println(")");
        }
    }
}

void StockMonitor::setUpdateInterval(unsigned long interval) {
    updateInterval = interval;
}

void StockMonitor::update() {
    unsigned long currentTime = millis();

    USBSerial.println("[股票监控] Update函数被调用");

    // 检查是否到达更新间隔（首次更新时lastUpdateTime为0，允许立即更新）
    if (lastUpdateTime != 0 && currentTime - lastUpdateTime < updateInterval) {
        USBSerial.println("[股票监控] 未到更新时间，跳过数据更新");
        // 更新滚动错误提示（UI效果每次都更新）
        updateScrollingError();
        return;
    }

    // 检查WiFi连接
    if (WiFi.status() != WL_CONNECTED) {
        lastError = "WiFi not connected";
        USBSerial.println("[股票监控] WiFi未连接");
        // 更新滚动错误提示
        updateScrollingError();
        return;
    }

    USBSerial.println("[股票监控] WiFi已连接，开始数据更新...");

    // 获取股票数据
    bool stockSuccess = fetchStockData();
    USBSerial.print("[股票监控] 股票数据获取结果: ");
    USBSerial.println(stockSuccess ? "成功" : "失败");

    // 获取指数数据
    bool indexSuccess = fetchIndexData();
    USBSerial.print("[股票监控] 指数数据获取结果: ");
    USBSerial.println(indexSuccess ? "成功" : "失败");

    if (stockSuccess || indexSuccess) {
        lastUpdateTime = currentTime;
        lastError = "";
        USBSerial.println("[股票监控] 数据更新完成");
    } else {
        lastError = "网络连接失败，请检查网络";
        USBSerial.println("[股票监控] 数据更新失败");

        // 临时添加测试数据以验证显示功能
        USBSerial.println("[股票监控] 使用临时测试数据");
        for (int i = 0; i < 2; i++) { // 只为前两个股票添加测试数据
            if (stockCodes[i].length() > 0) {
                stocks[i].name = "Test" + String(i + 1);
                stocks[i].currentPrice = 10.50 + i;
                stocks[i].changeAmount = 0.25 * (i % 2 == 0 ? 1 : -1);
                stocks[i].changePercent = 2.5 * (i % 2 == 0 ? 1 : -1);
                stocks[i].isValid = true;
                stocks[i].lastUpdate = millis();
            }
        }

        // 添加测试指数数据
        indices[0].currentValue = 3245.67;
        indices[0].changeAmount = 12.34;
        indices[0].changePercent = 0.38;
        indices[0].isValid = true;
        indices[0].lastUpdate = millis();

        indices[1].currentValue = 10856.32;
        indices[1].changeAmount = -25.68;
        indices[1].changePercent = -0.24;
        indices[1].isValid = true;
        indices[1].lastUpdate = millis();

        // 使用测试数据时也要更新lastUpdateTime
        lastUpdateTime = currentTime;
        USBSerial.println("[股票监控] 测试数据生成完成");
    }

    // 更新滚动错误提示
    updateScrollingError();
}

void StockMonitor::display() {
    USBSerial.println("[股票监控] 开始智能局部刷新");

    // 检查gfx指针
    if (!gfx) {
        USBSerial.println("[股票监控] 错误：gfx指针为空");
        return;
    }

    // 首次显示：绘制所有内容
    if (isFirstDisplay) {
        USBSerial.println("[股票监控] 首次显示，绘制所有内容");

        // 绘制上证指数
        if (indices[0].isValid) {
            drawIndexInfo(0, 20);
        }

        // 绘制股票信息
        int yPos = 60;
        for (int i = 0; i < 4; i++) {
            if (stocks[i].isValid && stockCodes[i].length() > 0) {
                drawStockInfo(i, yPos);
            }
            if (stockCodes[i].length() > 0) {
                yPos += 45;
            }
        }

        // 绘制深证成指
        drawIndexInfo(1, 220);

        // 绘制错误信息
        if (lastError.length() > 0) {
            hasError = true;
            drawScrollingError();
        }

        isFirstDisplay = false;
        updateDisplayCache();
        USBSerial.println("[股票监控] 首次显示完成");
        return;
    }

    // 临时移除startWrite/endWrite，避免嵌套调用问题
    // gfx->startWrite();

    // 检查上证指数是否需要更新
    bool indexChanged = hasIndexDataChanged(0);
    USBSerial.print("[股票监控] 上证指数变化检测: ");
    USBSerial.println(indexChanged ? "是" : "否");
    if (indexChanged) {
        USBSerial.println("[股票监控] 上证指数数据变化，局部刷新");
        USBSerial.println("[股票监控] 清除上证指数区域...");
        // 清除上证指数区域
        clearTextArea(0, 20, 240, 25);
        USBSerial.println("[股票监控] 重绘上证指数...");
        // 重绘上证指数
        if (indices[0].isValid) {
            drawIndexInfo(0, 20);
        }
        USBSerial.println("[股票监控] 上证指数刷新完成");
    }

    // 检查股票信息是否需要更新
    int yPos = 60;
    for (int i = 0; i < 4; i++) {
        if (stockCodes[i].length() > 0) {
            bool stockChanged = hasStockDataChanged(i);
            USBSerial.print("[股票监控] 股票");
            USBSerial.print(i + 1);
            USBSerial.print(" 变化检测: ");
            USBSerial.println(stockChanged ? "是" : "否");

            if (stockChanged) {
                USBSerial.print("[股票监控] 股票");
                USBSerial.print(i + 1);
                USBSerial.println(" 数据变化，局部刷新");

                // 清除该股票的显示区域
                clearTextArea(0, yPos, 240, 40);

                // 重绘该股票信息
                if (stocks[i].isValid) {
                    drawStockInfo(i, yPos);
                }
            }
            yPos += 45;
        }
    }

    // 检查深证成指是否需要更新
    bool szIndexChanged = hasIndexDataChanged(1);
    bool errorChanged = hasErrorChanged();
    USBSerial.print("[股票监控] 深证成指变化检测: ");
    USBSerial.println(szIndexChanged ? "是" : "否");
    USBSerial.print("[股票监控] 错误信息变化检测: ");
    USBSerial.println(errorChanged ? "是" : "否");

    if (szIndexChanged || errorChanged) {
        USBSerial.println("[股票监控] 深证成指或错误信息变化，局部刷新");
        // 清除深证成指区域
        clearTextArea(0, 220, 240, 20);
        // 重绘深证成指或错误信息
        drawIndexInfo(1, 220);
    }

    // 检查滚动错误信息
    if (lastError.length() > 0) {
        hasError = true;
        // 滚动错误信息需要持续更新
        clearTextArea(0, 230, 240, 10);
        drawScrollingError();
    } else {
        hasError = false;
    }

    // 保护性重绘：如果没有任何变化，确保屏幕内容完整
    bool anyChange = indexChanged || szIndexChanged || errorChanged;
    for (int i = 0; i < 4; i++) {
        if (stockCodes[i].length() > 0 && hasStockDataChanged(i)) {
            anyChange = true;
            break;
        }
    }

    if (!anyChange) {
        USBSerial.println("[股票监控] 没有数据变化，执行保护性重绘");
        // 重绘所有有效内容
        if (indices[0].isValid) {
            drawIndexInfo(0, 20);
        }

        int yPos = 60;
        for (int i = 0; i < 4; i++) {
            if (stocks[i].isValid && stockCodes[i].length() > 0) {
                drawStockInfo(i, yPos);
            }
            if (stockCodes[i].length() > 0) {
                yPos += 45;
            }
        }

        drawIndexInfo(1, 220);

        if (lastError.length() > 0) {
            drawScrollingError();
        }
    }

    // gfx->endWrite();

    // 更新显示缓存
    updateDisplayCache();

    USBSerial.println("[股票监控] 智能局部刷新完成");
}

bool StockMonitor::isDataReady() {
    // 检查是否至少有一个股票或指数数据有效
    for (int i = 0; i < 4; i++) {
        if (stocks[i].isValid) return true;
    }
    for (int i = 0; i < 2; i++) {
        if (indices[i].isValid) return true;
    }
    return false;
}

bool StockMonitor::isMarketOpen() {
    return isMarketTime();
}

String StockMonitor::getLastError() {
    return lastError;
}

StockData StockMonitor::getStockData(int index) {
    if (index >= 0 && index < 4) {
        return stocks[index];
    }
    StockData empty;
    empty.isValid = false;
    return empty;
}

IndexData StockMonitor::getIndexData(int index) {
    if (index >= 0 && index < 2) {
        return indices[index];
    }
    IndexData empty;
    empty.isValid = false;
    return empty;
}

bool StockMonitor::fetchStockData() {
    USBSerial.println("[Stock Monitor] Fetching stock data...");

    // 构建股票代码列表
    String stockList = "";
    int validStockCount = 0;

    for (int i = 0; i < 4; i++) {
        if (stockCodes[i].length() == 6) {
            if (validStockCount > 0) {
                stockList += ",";
            }
            // 根据股票代码添加交易所前缀
            if (stockCodes[i].startsWith("6")) {
                stockList += "sh" + stockCodes[i];  // 上交所
            } else if (stockCodes[i].startsWith("0") || stockCodes[i].startsWith("3")) {
                stockList += "sz" + stockCodes[i];  // 深交所
            }
            validStockCount++;

            USBSerial.print("[股票监控] 添加股票代码 ");
            USBSerial.print(i + 1);
            USBSerial.print(": ");
            USBSerial.println(stockCodes[i]);
        }
    }

    if (validStockCount == 0) {
        USBSerial.println("[Stock Monitor] No valid stock codes configured");
        return false;
    }

    // 构建新浪财经API URL
    String url = "http://hq.sinajs.cn/list=" + stockList;
    USBSerial.print("[Stock Monitor] API URL: ");
    USBSerial.println(url);

    // 发起HTTP请求
    USBSerial.println("[股票监控] 开始HTTP请求...");
    http.begin(url);
    http.setTimeout(HTTP_TIMEOUT);

    // 添加必要的请求头来避免403错误
    http.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    http.addHeader("Referer", "http://finance.sina.com.cn/");
    http.addHeader("Accept", "*/*");

    int httpCode = http.GET();
    USBSerial.print("[股票监控] HTTP响应代码: ");
    USBSerial.println(httpCode);

    if (httpCode == HTTP_CODE_OK) {
        String response = http.getString();
        USBSerial.println("[Stock Monitor] API response received");

        // 解析响应数据
        bool parseSuccess = parseStockResponse(response, validStockCount);
        http.end();

        if (parseSuccess) {
            USBSerial.println("[Stock Monitor] Stock data parsed successfully");
            return true;
        } else {
            lastError = "数据解析失败";
            USBSerial.println("[Stock Monitor] Failed to parse stock data");
            return false;
        }
    } else {
        lastError = "网络请求失败: " + String(httpCode);
        USBSerial.print("[股票监控] HTTP请求失败: ");
        USBSerial.println(httpCode);
        http.end();

        return false;
    }
}

bool StockMonitor::parseStockResponse(String response, int validStockCount) {
    USBSerial.println("[Stock Monitor] Parsing stock response...");

    // 按行分割响应数据
    int lineStart = 0;
    int stockIndex = 0;

    while (lineStart < response.length() && stockIndex < validStockCount && stockIndex < 4) {
        int lineEnd = response.indexOf('\n', lineStart);
        if (lineEnd == -1) lineEnd = response.length();

        String line = response.substring(lineStart, lineEnd);
        line.trim();

        if (line.length() > 0) {
            // 解析单行股票数据
            if (parseSingleStockLine(line, stockIndex)) {
                stockIndex++;
            }
        }

        lineStart = lineEnd + 1;
    }

    return stockIndex > 0;  // 至少解析成功一只股票
}

bool StockMonitor::parseSingleStockLine(String line, int index) {
    // 新浪财经API返回格式：
    // var hq_str_sh000001="股票名称,今日开盘价,昨日收盘价,当前价格,今日最高价,今日最低价,买一价,卖一价,成交股数,成交金额,买一量,买一价,买二量,买二价,...,日期,时间,00";

    // 查找引号内的数据
    int startQuote = line.indexOf('"');
    int endQuote = line.lastIndexOf('"');

    if (startQuote == -1 || endQuote == -1 || startQuote >= endQuote) {
        USBSerial.println("[Stock Monitor] Invalid line format");
        return false;
    }

    String data = line.substring(startQuote + 1, endQuote);

    // 按逗号分割数据
    int commaCount = 0;
    int lastComma = -1;
    String fields[6];  // 只需要前6个字段：名称,开盘,昨收,现价,最高,最低

    for (int i = 0; i < data.length() && commaCount < 6; i++) {
        if (data.charAt(i) == ',') {
            fields[commaCount] = data.substring(lastComma + 1, i);
            lastComma = i;
            commaCount++;
        }
    }

    if (commaCount < 3) {
        USBSerial.println("[Stock Monitor] Insufficient data fields");
        return false;
    }

    // 填充股票数据
    stocks[index].name = fields[0];                    // 股票名称
    stocks[index].currentPrice = fields[3].toFloat(); // 当前价格
    float yesterdayClose = fields[2].toFloat();        // 昨日收盘价

    // 计算涨跌额和涨跌幅
    stocks[index].changeAmount = stocks[index].currentPrice - yesterdayClose;
    if (yesterdayClose > 0) {
        stocks[index].changePercent = (stocks[index].changeAmount / yesterdayClose) * 100.0;
    } else {
        stocks[index].changePercent = 0.0;
    }

    stocks[index].isValid = true;
    stocks[index].lastUpdate = millis();

    USBSerial.print("[Stock Monitor] Parsed: ");
    USBSerial.print(stocks[index].name);
    USBSerial.print(" ");
    USBSerial.print(stocks[index].currentPrice);
    USBSerial.print(" ");
    USBSerial.print(stocks[index].changePercent);
    USBSerial.println("%");

    return true;
}

bool StockMonitor::fetchIndexData() {
    USBSerial.println("[Stock Monitor] Fetching index data...");

    // 构建指数API URL：上证指数(sh000001)和深证成指(sz399001)
    String url = "http://hq.sinajs.cn/list=sh000001,sz399001";
    USBSerial.print("[Stock Monitor] Index API URL: ");
    USBSerial.println(url);

    // 发起HTTP请求
    http.begin(url);
    http.setTimeout(HTTP_TIMEOUT);

    // 添加必要的请求头来避免403错误
    http.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    http.addHeader("Referer", "http://finance.sina.com.cn/");
    http.addHeader("Accept", "*/*");

    int httpCode = http.GET();

    if (httpCode == HTTP_CODE_OK) {
        String response = http.getString();
        USBSerial.println("[Stock Monitor] Index API response received");

        // 解析响应数据
        bool parseSuccess = parseIndexResponse(response);
        http.end();

        if (parseSuccess) {
            USBSerial.println("[Stock Monitor] Index data parsed successfully");
            return true;
        } else {
            lastError = "指数数据解析失败";
            USBSerial.println("[Stock Monitor] Failed to parse index data");
            return false;
        }
    } else {
        lastError = "指数数据请求失败: " + String(httpCode);
        USBSerial.print("[股票监控] 指数HTTP请求失败: ");
        USBSerial.println(httpCode);
        http.end();

        return false;
    }
}

bool StockMonitor::parseIndexResponse(String response) {
    USBSerial.println("[Stock Monitor] Parsing index response...");

    // 按行分割响应数据
    int lineStart = 0;
    int indexCount = 0;

    while (lineStart < response.length() && indexCount < 2) {
        int lineEnd = response.indexOf('\n', lineStart);
        if (lineEnd == -1) lineEnd = response.length();

        String line = response.substring(lineStart, lineEnd);
        line.trim();

        if (line.length() > 0) {
            // 解析单行指数数据
            if (parseSingleIndexLine(line, indexCount)) {
                indexCount++;
            }
        }

        lineStart = lineEnd + 1;
    }

    return indexCount > 0;  // 至少解析成功一个指数
}

bool StockMonitor::parseSingleIndexLine(String line, int index) {
    // 查找引号内的数据
    int startQuote = line.indexOf('"');
    int endQuote = line.lastIndexOf('"');

    if (startQuote == -1 || endQuote == -1 || startQuote >= endQuote) {
        USBSerial.println("[Stock Monitor] Invalid index line format");
        return false;
    }

    String data = line.substring(startQuote + 1, endQuote);

    // 按逗号分割数据
    int commaCount = 0;
    int lastComma = -1;
    String fields[6];  // 只需要前6个字段：名称,开盘,昨收,现价,最高,最低

    for (int i = 0; i < data.length() && commaCount < 6; i++) {
        if (data.charAt(i) == ',') {
            fields[commaCount] = data.substring(lastComma + 1, i);
            lastComma = i;
            commaCount++;
        }
    }

    if (commaCount < 3) {
        USBSerial.println("[Stock Monitor] Insufficient index data fields");
        return false;
    }

    // 填充指数数据
    indices[index].currentValue = fields[3].toFloat(); // 当前点位
    float yesterdayClose = fields[2].toFloat();        // 昨日收盘

    // 计算涨跌点数和涨跌幅
    indices[index].changeAmount = indices[index].currentValue - yesterdayClose;
    if (yesterdayClose > 0) {
        indices[index].changePercent = (indices[index].changeAmount / yesterdayClose) * 100.0;
    } else {
        indices[index].changePercent = 0.0;
    }

    indices[index].isValid = true;
    indices[index].lastUpdate = millis();

    USBSerial.print("[Stock Monitor] Parsed index: ");
    USBSerial.print(indices[index].name);
    USBSerial.print(" ");
    USBSerial.print(indices[index].currentValue);
    USBSerial.print(" ");
    USBSerial.print(indices[index].changePercent);
    USBSerial.println("%");

    return true;
}

void StockMonitor::clearScreen() {
    // 智能局部刷新模式：只在首次显示时清屏
    if (gfx) {
        gfx->fillScreen(BLACK);
    }
}

void StockMonitor::resetFirstDisplay() {
    isFirstDisplay = true;
    USBSerial.println("[股票监控] 重置首次显示标志");
}

void StockMonitor::drawStockInfo(int index, int16_t y) {
    if (!stocks[index].isValid || !gfx) return;

    // 直接绘制模式，优化后的局部刷新
    gfx->setTextSize(2);
    gfx->setTextColor(WHITE);
    gfx->setCursor(5, y);
    gfx->print(stockCodes[index]);

    gfx->setCursor(5, y + 20);
    gfx->print("RMB:");
    gfx->print(stocks[index].currentPrice, 2);

    uint16_t changeColor = getChangeColor(stocks[index].changePercent);
    gfx->setTextColor(changeColor);
    gfx->setCursor(120, y + 20);
    if (stocks[index].changePercent > 0) {
        gfx->print("+");
    }
    gfx->print(stocks[index].changePercent, 2);
    gfx->print("%");

    USBSerial.print("[股票监控] 绘制股票 ");
    USBSerial.print(index + 1);
    USBSerial.print(": ");
    USBSerial.println(stocks[index].name);
}

void StockMonitor::drawIndexInfo(int index, int16_t y) {
    if (!gfx) return;

    // 如果有错误且是深证成指区域，显示错误信息
    if (index == 1 && lastError.length() > 0) {
        gfx->setTextSize(1);
        gfx->setTextColor(RED);
        gfx->setCursor(5, y);
        gfx->print("ERROR");
        return;
    }

    // 正常显示指数信息
    if (!indices[index].isValid) return;

    gfx->setTextSize(2);
    gfx->setTextColor(WHITE);
    gfx->setCursor(5, y);
    if (index == 0) {
        gfx->print("SH ");
    } else {
        gfx->print("SZ ");
    }
    gfx->print(indices[index].currentValue, 2);

    uint16_t changeColor = getChangeColor(indices[index].changePercent);
    gfx->setTextColor(changeColor);
    gfx->print(" ");
    if (indices[index].changePercent > 0) {
        gfx->print("+");
    }
    gfx->print(indices[index].changePercent, 2);
    gfx->print("%");

    USBSerial.print("[股票监控] 绘制指数: ");
    USBSerial.println(indices[index].name);
}



void StockMonitor::drawScrollingError() {
    if (!hasError || lastError.length() == 0 || !gfx) return;

    // 在屏幕底部显示滚动错误信息
    int16_t y = 230;  // 底部位置

    gfx->setTextSize(1);
    gfx->setTextColor(RED);

    // 计算文本宽度（大概估算）
    String errorText = "Network Error";
    int textWidth = errorText.length() * 6;  // 每个字符约6像素宽

    // 滚动显示
    int16_t x = 240 - scrollOffset;
    if (x + textWidth < 0) {
        scrollOffset = 0;  // 重新开始滚动
        x = 240;
    }

    gfx->setCursor(x, y);
    gfx->print(errorText);
}

void StockMonitor::updateScrollingError() {
    if (!hasError) return;

    unsigned long currentTime = millis();

    // 每100毫秒移动2像素，速度不会太快
    if (currentTime - lastScrollTime >= 100) {
        scrollOffset += 2;
        lastScrollTime = currentTime;
    }
}

uint16_t StockMonitor::getChangeColor(float changePercent) {
    if (changePercent > 0) {
        return RED;    // 上涨红色
    } else if (changePercent < 0) {
        return GREEN;  // 下跌绿色
    } else {
        return WHITE;  // 平盘白色
    }
}

String StockMonitor::getExchangeName(String code) {
    if (code.startsWith("6")) {
        return "上交所";
    } else if (code.startsWith("0")) {
        return "深交所";
    } else if (code.startsWith("3")) {
        return "创业板";
    }
    return "未知";
}

bool StockMonitor::isValidStockCode(String code) {
    // 检查是否为6位数字
    if (code.length() != 6) return false;
    
    for (int i = 0; i < 6; i++) {
        if (!isDigit(code.charAt(i))) return false;
    }
    
    // 检查是否为有效的交易所代码
    return code.startsWith("6") || code.startsWith("0") || code.startsWith("3");
}

bool StockMonitor::isMarketTime() {
    // TODO: 实现市场时间检查
    // 简单实现：假设工作日9:30-15:00为交易时间
    return true; // 暂时返回true，后续实现具体逻辑
}



// 智能局部刷新函数实现
bool StockMonitor::hasStockDataChanged(int index) {
    if (index < 0 || index >= 4) return false;

    // 检查有效性变化
    if (stocks[index].isValid != lastDisplayedStocks[index].isValid) {
        return true;
    }

    // 如果都无效，则没有变化
    if (!stocks[index].isValid && !lastDisplayedStocks[index].isValid) {
        return false;
    }

    // 检查价格变化（精度到小数点后2位）
    if (abs(stocks[index].currentPrice - lastDisplayedStocks[index].currentPrice) > 0.005) {
        return true;
    }

    // 检查涨跌幅变化（精度到小数点后2位）
    if (abs(stocks[index].changePercent - lastDisplayedStocks[index].changePercent) > 0.005) {
        return true;
    }

    return false;
}

bool StockMonitor::hasIndexDataChanged(int index) {
    if (index < 0 || index >= 2) return false;

    // 检查有效性变化
    if (indices[index].isValid != lastDisplayedIndices[index].isValid) {
        return true;
    }

    // 如果都无效，则没有变化
    if (!indices[index].isValid && !lastDisplayedIndices[index].isValid) {
        return false;
    }

    // 检查数值变化（精度到小数点后2位）
    if (abs(indices[index].currentValue - lastDisplayedIndices[index].currentValue) > 0.005) {
        return true;
    }

    // 检查涨跌幅变化（精度到小数点后2位）
    if (abs(indices[index].changePercent - lastDisplayedIndices[index].changePercent) > 0.005) {
        return true;
    }

    return false;
}

bool StockMonitor::hasErrorChanged() {
    return lastError != lastDisplayedError;
}

void StockMonitor::updateDisplayCache() {
    // 更新股票数据缓存
    for (int i = 0; i < 4; i++) {
        lastDisplayedStocks[i] = stocks[i];
    }

    // 更新指数数据缓存
    for (int i = 0; i < 2; i++) {
        lastDisplayedIndices[i] = indices[i];
    }

    // 更新错误信息缓存
    lastDisplayedError = lastError;
}

void StockMonitor::clearTextArea(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (gfx) {
        gfx->fillRect(x, y, w, h, BLACK);
    }
}

// 股票专用双缓冲函数实现
bool StockMonitor::initStockFrameBuffer() {
    if (stockFrameBuffer == nullptr) {
        // 检查是否有PSRAM可用
        if (psramFound()) {
            // 为240x240全屏创建缓冲区
            stockFrameBuffer = (uint16_t*)ps_malloc(240 * 240 * sizeof(uint16_t));
        } else {
            // 如果没有PSRAM，尝试使用普通内存
            stockFrameBuffer = (uint16_t*)malloc(240 * 240 * sizeof(uint16_t));
        }

        if (stockFrameBuffer == nullptr) {
            return false;
        }

        // 初始化为黑色背景
        clearStockFrameBuffer();
        useStockFrameBuffer = true;
    }
    return true;
}

void StockMonitor::renderStockFrameBuffer() {
    if (stockFrameBuffer != nullptr && useStockFrameBuffer && gfx) {
        // 使用draw16bitRGBBitmap进行DMA批量传输，大幅提升刷新速度
        gfx->draw16bitRGBBitmap(0, 0, stockFrameBuffer, 240, 240);
    }
}

void StockMonitor::clearStockFrameBuffer() {
    if (stockFrameBuffer != nullptr) {
        // 清除为黑色背景
        for (int i = 0; i < 240 * 240; i++) {
            stockFrameBuffer[i] = BLACK;
        }
    }
}

void StockMonitor::drawPixelToStockBuffer(int16_t x, int16_t y, uint16_t color) {
    if (stockFrameBuffer != nullptr && x >= 0 && x < 240 && y >= 0 && y < 240) {
        stockFrameBuffer[y * 240 + x] = color;
    }
}

void StockMonitor::drawTextToStockBuffer(String text, int16_t x, int16_t y, uint16_t color, int size) {
    if (!useStockFrameBuffer || stockFrameBuffer == nullptr) {
        return;
    }

    // 简单的5x7点阵字体，支持数字、字母和基本符号
    const uint8_t font5x7[][5] = {
        {0x3E, 0x51, 0x49, 0x45, 0x3E}, // 0
        {0x00, 0x42, 0x7F, 0x40, 0x00}, // 1
        {0x42, 0x61, 0x51, 0x49, 0x46}, // 2
        {0x21, 0x41, 0x45, 0x4B, 0x31}, // 3
        {0x18, 0x14, 0x12, 0x7F, 0x10}, // 4
        {0x27, 0x45, 0x45, 0x45, 0x39}, // 5
        {0x3C, 0x4A, 0x49, 0x49, 0x30}, // 6
        {0x01, 0x71, 0x09, 0x05, 0x03}, // 7
        {0x36, 0x49, 0x49, 0x49, 0x36}, // 8
        {0x06, 0x49, 0x49, 0x29, 0x1E}, // 9
        {0x7C, 0x12, 0x11, 0x12, 0x7C}, // A
        {0x7F, 0x49, 0x49, 0x49, 0x36}, // B
        {0x3E, 0x41, 0x41, 0x41, 0x22}, // C
        {0x7F, 0x41, 0x41, 0x22, 0x1C}, // D
        {0x7F, 0x49, 0x49, 0x49, 0x41}, // E
        {0x7F, 0x09, 0x09, 0x09, 0x01}, // F
        {0x3E, 0x41, 0x49, 0x49, 0x7A}, // G
        {0x7F, 0x08, 0x08, 0x08, 0x7F}, // H
        {0x00, 0x41, 0x7F, 0x41, 0x00}, // I
        {0x20, 0x40, 0x41, 0x3F, 0x01}, // J
        {0x7F, 0x08, 0x14, 0x22, 0x41}, // K
        {0x7F, 0x40, 0x40, 0x40, 0x40}, // L
        {0x7F, 0x02, 0x0C, 0x02, 0x7F}, // M
        {0x7F, 0x04, 0x08, 0x10, 0x7F}, // N
        {0x3E, 0x41, 0x41, 0x41, 0x3E}, // O
        {0x7F, 0x09, 0x09, 0x09, 0x06}, // P
        {0x3E, 0x41, 0x51, 0x21, 0x5E}, // Q
        {0x7F, 0x09, 0x19, 0x29, 0x46}, // R
        {0x46, 0x49, 0x49, 0x49, 0x31}, // S
        {0x01, 0x01, 0x7F, 0x01, 0x01}, // T
        {0x3F, 0x40, 0x40, 0x40, 0x3F}, // U
        {0x1F, 0x20, 0x40, 0x20, 0x1F}, // V
        {0x3F, 0x40, 0x38, 0x40, 0x3F}, // W
        {0x63, 0x14, 0x08, 0x14, 0x63}, // X
        {0x07, 0x08, 0x70, 0x08, 0x07}, // Y
        {0x61, 0x51, 0x49, 0x45, 0x43}, // Z
        {0x00, 0x00, 0x00, 0x00, 0x00}, // 空格
        {0x00, 0x00, 0x5F, 0x00, 0x00}, // !
        {0x00, 0x36, 0x36, 0x00, 0x00}, // :
        {0x08, 0x08, 0x08, 0x08, 0x08}, // -
        {0x00, 0x60, 0x60, 0x00, 0x00}, // .
        {0x14, 0x7F, 0x14, 0x7F, 0x14}, // #
        {0x24, 0x2A, 0x7F, 0x2A, 0x12}, // $
        {0x23, 0x13, 0x08, 0x64, 0x62}, // %
        {0x08, 0x14, 0x22, 0x41, 0x00}, // <
        {0x00, 0x41, 0x22, 0x14, 0x08}, // >
        {0x14, 0x14, 0x14, 0x14, 0x14}  // =
    };

    for (int i = 0; i < text.length(); i++) {
        char c = text.charAt(i);
        int fontIndex = -1;

        // 映射字符到字体索引
        if (c >= '0' && c <= '9') {
            fontIndex = c - '0';
        } else if (c >= 'A' && c <= 'Z') {
            fontIndex = c - 'A' + 10;
        } else if (c >= 'a' && c <= 'z') {
            fontIndex = c - 'a' + 10; // 小写字母使用大写字体
        } else if (c == ' ') {
            fontIndex = 36;
        } else if (c == '!') {
            fontIndex = 37;
        } else if (c == ':') {
            fontIndex = 38;
        } else if (c == '-') {
            fontIndex = 39;
        } else if (c == '.') {
            fontIndex = 40;
        } else if (c == '#') {
            fontIndex = 41;
        } else if (c == '$') {
            fontIndex = 42;
        } else if (c == '%') {
            fontIndex = 43;
        } else if (c == '<') {
            fontIndex = 44;
        } else if (c == '>') {
            fontIndex = 45;
        } else if (c == '=') {
            fontIndex = 46;
        }

        if (fontIndex >= 0) {
            // 绘制字符到缓冲区
            for (int col = 0; col < 5; col++) {
                uint8_t columnData = font5x7[fontIndex][col];
                for (int row = 0; row < 7; row++) {
                    if (columnData & (1 << row)) {
                        // 根据size参数进行缩放
                        for (int sy = 0; sy < size; sy++) {
                            for (int sx = 0; sx < size; sx++) {
                                int px = x + i * (6 * size) + col * size + sx;
                                int py = y + row * size + sy;
                                drawPixelToStockBuffer(px, py, color);
                            }
                        }
                    }
                }
            }
        }
    }
}
