#include "photo_gallery.h"

// 外部声明
extern Arduino_GFX *gfx;
extern HWCDC USBSerial;

// 静态常量定义
const char* PhotoGallery::PHOTO_FILE_PATH = "/gallery/current_photo.jpg";
const char* PhotoGallery::PHOTO_INFO_PATH = "/gallery/photo_info.dat";

// 静态变量定义
PhotoGallery* PhotoGallery::currentInstance = nullptr;
File* PhotoGallery::currentOutputFile = nullptr;
ScaleParams PhotoGallery::currentScaleParams = {0};

PhotoGallery::PhotoGallery() {
    galleryFrameBuffer = nullptr;  // 保留变量但不使用
    useFrameBuffer = false;
    photoExists = false;
    lastError = "";

    // 初始化元数据
    memset(&currentMetadata, 0, sizeof(PhotoMetadata));
}

PhotoGallery::~PhotoGallery() {
    // 扫描线模式不需要大缓冲区，无需释放
    // galleryFrameBuffer在扫描线模式下不使用
}

void PhotoGallery::init() {
    USBSerial.println("=== Photo Gallery Module Initializing ===");

    // 显示当前内存状态
    USBSerial.printf("初始化前 - 可用内存: %d 字节\n", ESP.getFreeHeap());
    if (psramFound()) {
        USBSerial.printf("PSRAM可用 - 总计: %d 字节, 可用: %d 字节\n",
                        ESP.getPsramSize(), ESP.getFreePsram());
    }

    // 显示LittleFS空间信息
    USBSerial.println("=== LittleFS 空间信息 ===");
    USBSerial.printf("总空间: %d 字节 (%.2f MB)\n", LittleFS.totalBytes(), LittleFS.totalBytes() / 1024.0 / 1024.0);
    USBSerial.printf("已用空间: %d 字节 (%.2f MB)\n", LittleFS.usedBytes(), LittleFS.usedBytes() / 1024.0 / 1024.0);
    USBSerial.printf("可用空间: %d 字节 (%.2f MB)\n",
                    LittleFS.totalBytes() - LittleFS.usedBytes(),
                    (LittleFS.totalBytes() - LittleFS.usedBytes()) / 1024.0 / 1024.0);

    // 详细分析LittleFS空间使用情况
    analyzeLittleFSUsage();

    // 检查空间是否严重不足，如果是则进行清理
    size_t freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
    if (freeBytes < 100000) {  // 如果可用空间少于100KB
        USBSerial.printf("警告：LittleFS可用空间不足(%d字节)，开始清理\n", freeBytes);
        cleanupGalleryDirectory();

        // 重新检查空间
        freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
        USBSerial.printf("清理后可用空间: %d 字节\n", freeBytes);
    }

    // 检查是否有照片存在
    photoExists = LittleFS.exists(PHOTO_FILE_PATH) && LittleFS.exists(PHOTO_INFO_PATH);

    if (photoExists) {
        // 加载照片元数据
        if (loadPhotoMetadata()) {
            USBSerial.println("Photo found, metadata loaded successfully");
            USBSerial.printf("Photo: %s (%dx%d -> %dx%d)\n",
                            currentMetadata.originalName,
                            currentMetadata.originalWidth, currentMetadata.originalHeight,
                            currentMetadata.displayWidth, currentMetadata.displayHeight);
        } else {
            USBSerial.println("Photo found but metadata load failed");
            photoExists = false;
        }
    } else {
        USBSerial.println("No photo found");
    }

    USBSerial.printf("初始化后 - 可用内存: %d 字节\n", ESP.getFreeHeap());
    USBSerial.println("Photo Gallery Module initialized successfully");
}

void PhotoGallery::display() {
    // 清除屏幕为黑色背景
    gfx->fillScreen(0x0000);  // 黑色

    if (photoExists) {
        // 从文件读取并显示照片
        drawPhoto();
    } else {
        // 显示无照片消息
        drawNoPhotoMessage();
    }
}

void PhotoGallery::update() {
    // 静态照片显示，无需频繁更新
    // 预留接口用于未来功能扩展
}

bool PhotoGallery::savePhoto(const uint8_t* data, size_t size, uint16_t originalWidth, uint16_t originalHeight, const String& originalName) {
    USBSerial.printf("Saving photo: %s (%dx%d, %d bytes)\n", originalName.c_str(), originalWidth, originalHeight, size);
    
    // 计算缩放参数
    ScaleParams params = calculateScale(originalWidth, originalHeight);
    
    // 保存照片数据
    if (!savePhotoData(data, size)) {
        lastError = "Failed to save photo data";
        return false;
    }
    
    // 创建元数据
    PhotoMetadata metadata;
    metadata.originalWidth = originalWidth;
    metadata.originalHeight = originalHeight;
    metadata.displayWidth = params.dstW;
    metadata.displayHeight = params.dstH;
    metadata.offsetX = params.offsetX;
    metadata.offsetY = params.offsetY;
    metadata.fileSize = size;
    
    // 设置上传时间（简化版本）
    strcpy(metadata.uploadTime, "2024-01-01 00:00:00");
    
    // 设置原始文件名
    strncpy(metadata.originalName, originalName.c_str(), sizeof(metadata.originalName) - 1);
    metadata.originalName[sizeof(metadata.originalName) - 1] = '\0';
    
    // 保存元数据
    if (!savePhotoMetadata(metadata)) {
        lastError = "Failed to save photo metadata";
        return false;
    }
    
    // 更新当前状态
    currentMetadata = metadata;
    photoExists = true;
    
    USBSerial.println("Photo saved successfully");
    return true;
}

bool PhotoGallery::hasPhoto() {
    return photoExists;
}

void PhotoGallery::deletePhoto() {
    USBSerial.println("Deleting current photo");
    
    // 删除文件
    if (LittleFS.exists(PHOTO_FILE_PATH)) {
        LittleFS.remove(PHOTO_FILE_PATH);
    }
    
    if (LittleFS.exists(PHOTO_INFO_PATH)) {
        LittleFS.remove(PHOTO_INFO_PATH);
    }
    
    // 重置状态
    photoExists = false;
    memset(&currentMetadata, 0, sizeof(PhotoMetadata));
    
    USBSerial.println("Photo deleted successfully");
}

bool PhotoGallery::loadPhotoMetadata() {
    File infoFile = LittleFS.open(PHOTO_INFO_PATH, "r");
    if (!infoFile) {
        lastError = "Cannot open photo info file";
        return false;
    }
    
    size_t bytesRead = infoFile.read((uint8_t*)&currentMetadata, sizeof(PhotoMetadata));
    infoFile.close();
    
    if (bytesRead != sizeof(PhotoMetadata)) {
        lastError = "Photo info file corrupted";
        return false;
    }
    
    return true;
}

ScaleParams PhotoGallery::calculateScale(uint16_t srcW, uint16_t srcH) {
    ScaleParams params;
    
    // 计算缩放比例（保持宽高比）
    float scaleX = (float)SCREEN_SIZE / srcW;
    float scaleY = (float)SCREEN_SIZE / srcH;
    params.scale = min(scaleX, scaleY);
    
    // 计算目标尺寸
    params.dstW = (uint16_t)(srcW * params.scale);
    params.dstH = (uint16_t)(srcH * params.scale);
    
    // 计算居中偏移
    params.offsetX = (SCREEN_SIZE - params.dstW) / 2;
    params.offsetY = (SCREEN_SIZE - params.dstH) / 2;
    
    return params;
}

void PhotoGallery::drawPhoto() {
    USBSerial.println("开始绘制照片");

    // 检查照片文件是否存在
    if (!LittleFS.exists(PHOTO_FILE_PATH)) {
        USBSerial.println("错误：照片文件不存在");
        drawNoPhotoMessage();
        return;
    }

    // 使用decodeAndDisplayJPEG函数来显示已保存的JPEG
    if (!decodeAndDisplayJPEG(currentMetadata.originalName)) {
        USBSerial.println("错误：JPEG解码显示失败");
        drawNoPhotoMessage();
        return;
    }

    USBSerial.println("照片绘制完成");
}

void PhotoGallery::drawNoPhotoMessage() {
    // 显示无照片提示
    gfx->setTextColor(0xFFFF);  // 白色

    // "NO PHOTO" - 文字大小2，居中对齐
    gfx->setTextSize(2);
    String mainText = "NO PHOTO";
    int mainTextWidth = mainText.length() * 12;  // 每个字符约12像素宽
    int mainTextX = (SCREEN_SIZE - mainTextWidth) / 2;
    gfx->setCursor(mainTextX, 110);
    gfx->print(mainText);

    // "Upload via web interface" - 文字大小1，居中对齐
    gfx->setTextSize(1);
    String subText = "Upload via web interface";
    int subTextWidth = subText.length() * 6;  // 每个字符约6像素宽
    int subTextX = (SCREEN_SIZE - subTextWidth) / 2;
    gfx->setCursor(subTextX, 140);
    gfx->print(subText);
}

bool PhotoGallery::savePhotoData(const uint8_t* data, size_t size) {
    File photoFile = LittleFS.open(PHOTO_FILE_PATH, "w");
    if (!photoFile) {
        return false;
    }
    
    size_t written = photoFile.write(data, size);
    photoFile.close();
    
    return written == size;
}

bool PhotoGallery::savePhotoMetadata(const PhotoMetadata& metadata) {
    File infoFile = LittleFS.open(PHOTO_INFO_PATH, "w");
    if (!infoFile) {
        return false;
    }
    
    size_t written = infoFile.write((uint8_t*)&metadata, sizeof(PhotoMetadata));
    infoFile.close();
    
    return written == sizeof(PhotoMetadata);
}

bool PhotoGallery::loadPhotoData() {
    // 预留接口，用于加载照片数据到内存
    // 将在实现图片显示功能时完善
    return true;
}

// 双缓冲系统预留接口
bool PhotoGallery::initGalleryFrameBuffer() {
    // 预留：240x240双缓冲初始化
    // galleryFrameBuffer = (uint16_t*)malloc(SCREEN_SIZE * SCREEN_SIZE * sizeof(uint16_t));
    // return galleryFrameBuffer != nullptr;
    return false;  // 当前不使用双缓冲
}

void PhotoGallery::renderGalleryFrameBuffer() {
    // 预留：渲染帧缓冲到屏幕
    // if (galleryFrameBuffer != nullptr && useFrameBuffer) {
    //     gfx->draw16bitRGBBitmap(0, 0, galleryFrameBuffer, SCREEN_SIZE, SCREEN_SIZE);
    // }
}

void PhotoGallery::clearGalleryFrameBuffer() {
    // 预留：清除帧缓冲
    // if (galleryFrameBuffer != nullptr) {
    //     memset(galleryFrameBuffer, 0, SCREEN_SIZE * SCREEN_SIZE * sizeof(uint16_t));
    // }
}

// 图片处理实现
bool PhotoGallery::processImageFile(const String& tempFilePath, const String& originalName) {
    USBSerial.printf("开始处理图片文件: %s\n", originalName.c_str());

    // 打开临时文件
    File tempFile = LittleFS.open(tempFilePath, "r");
    if (!tempFile) {
        lastError = "无法打开临时文件";
        USBSerial.println("错误：无法打开临时文件");
        return false;
    }

    // 根据文件扩展名判断格式
    String fileName = originalName;
    fileName.toLowerCase();

    bool success = false;
    if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
        USBSerial.println("检测到JPEG格式");
        success = processJPEG(tempFile, originalName);
    } else if (fileName.endsWith(".bmp")) {
        USBSerial.println("检测到BMP格式");
        success = processBMP(tempFile, originalName);
    } else {
        lastError = "不支持的文件格式";
        USBSerial.println("错误：不支持的文件格式");
    }

    tempFile.close();

    // 清理临时文件
    LittleFS.remove(tempFilePath);

    if (success) {
        USBSerial.println("图片处理成功");
    } else {
        USBSerial.printf("图片处理失败：%s\n", lastError.c_str());
    }

    return success;
}

// JPEG处理相关函数

bool PhotoGallery::processJPEG(File& file, const String& originalName) {
    USBSerial.println("开始处理JPEG文件");

    // 获取文件大小
    size_t fileSize = file.size();
    USBSerial.printf("JPEG文件大小: %d字节\n", fileSize);

    // 显示当前LittleFS状态
    USBSerial.println("=== 处理前LittleFS状态 ===");
    USBSerial.printf("总空间: %d 字节\n", LittleFS.totalBytes());
    USBSerial.printf("已用空间: %d 字节\n", LittleFS.usedBytes());
    USBSerial.printf("可用空间: %d 字节\n", LittleFS.totalBytes() - LittleFS.usedBytes());

    // 强力清理gallery目录以释放空间
    USBSerial.println("开始强力清理gallery目录");
    cleanupGalleryDirectory();

    // 显示清理后的空间状态
    USBSerial.println("=== 清理后LittleFS状态 ===");
    USBSerial.printf("总空间: %d 字节\n", LittleFS.totalBytes());
    USBSerial.printf("已用空间: %d 字节\n", LittleFS.usedBytes());
    USBSerial.printf("可用空间: %d 字节\n", LittleFS.totalBytes() - LittleFS.usedBytes());

    // 检查可用空间是否足够
    size_t freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
    if (freeBytes < fileSize + 10000) {  // 预留10KB空间
        lastError = "存储空间不足";
        USBSerial.printf("错误：存储空间不足，需要%d字节，可用%d字节\n", fileSize + 10000, freeBytes);
        return false;
    }

    // 步骤1：先保存原始JPEG文件到存储
    USBSerial.println("步骤1：保存原始JPEG文件到存储");
    if (!saveOriginalJPEG(file, originalName)) {
        lastError = "保存原始JPEG文件失败";
        return false;
    }

    // 步骤2：从存储的JPEG文件逐行解码显示
    USBSerial.println("步骤2：从存储文件逐行解码显示");
    if (!decodeAndDisplayJPEG(originalName)) {
        lastError = "JPEG解码显示失败";
        // 清理失败的文件
        LittleFS.remove(PHOTO_FILE_PATH);
        LittleFS.remove(PHOTO_INFO_PATH);
        return false;
    }

    USBSerial.println("JPEG处理完成：保存+显示成功");
    return true;
}

// 新的JPEG处理方法实现

bool PhotoGallery::saveOriginalJPEG(File& file, const String& originalName) {
    USBSerial.println("保存原始JPEG文件到存储");

    // 获取文件大小
    size_t fileSize = file.size();

    // 先获取JPEG尺寸信息
    uint16_t jpegWidth, jpegHeight;
    if (!getJPEGDimensions(file, jpegWidth, jpegHeight)) {
        return false;
    }

    USBSerial.printf("JPEG尺寸: %dx%d\n", jpegWidth, jpegHeight);

    // 计算缩放参数并保存元数据
    ScaleParams params = calculateScale(jpegWidth, jpegHeight);
    USBSerial.printf("缩放参数: 比例=%.2f, 目标尺寸=%dx%d, 偏移=(%d,%d)\n",
                    params.scale, params.dstW, params.dstH, params.offsetX, params.offsetY);

    PhotoMetadata metadata;
    metadata.originalWidth = jpegWidth;
    metadata.originalHeight = jpegHeight;
    metadata.displayWidth = params.dstW;
    metadata.displayHeight = params.dstH;
    metadata.offsetX = params.offsetX;
    metadata.offsetY = params.offsetY;
    metadata.fileSize = fileSize;
    strcpy(metadata.uploadTime, "2024-01-01 00:00:00");
    strncpy(metadata.originalName, originalName.c_str(), sizeof(metadata.originalName) - 1);
    metadata.originalName[sizeof(metadata.originalName) - 1] = '\0';

    // 保存元数据
    if (!savePhotoMetadata(metadata)) {
        lastError = "元数据保存失败";
        return false;
    }

    // 保存原始JPEG文件（分块复制以节省内存）
    File jpegFile = LittleFS.open(PHOTO_FILE_PATH, "w");
    if (!jpegFile) {
        lastError = "无法创建JPEG文件";
        return false;
    }

    // 使用小缓冲区分块复制文件
    const size_t COPY_BUFFER_SIZE = 1024;  // 1KB缓冲区
    uint8_t* copyBuffer = (uint8_t*)malloc(COPY_BUFFER_SIZE);
    if (!copyBuffer) {
        jpegFile.close();
        lastError = "无法分配复制缓冲区";
        return false;
    }

    file.seek(0);
    size_t totalCopied = 0;

    while (totalCopied < fileSize) {
        size_t toRead = min(COPY_BUFFER_SIZE, fileSize - totalCopied);
        size_t bytesRead = file.read(copyBuffer, toRead);

        if (bytesRead == 0) {
            break;  // 读取结束
        }

        size_t bytesWritten = jpegFile.write(copyBuffer, bytesRead);
        if (bytesWritten != bytesRead) {
            free(copyBuffer);
            jpegFile.close();
            lastError = "文件写入失败";
            return false;
        }

        totalCopied += bytesRead;

        // 显示进度
        if (totalCopied % (10 * 1024) == 0 || totalCopied == fileSize) {
            USBSerial.printf("已复制: %d/%d 字节 (%.1f%%)\n",
                           totalCopied, fileSize, (float)totalCopied * 100.0 / fileSize);
        }
    }

    free(copyBuffer);
    jpegFile.close();

    if (totalCopied != fileSize) {
        lastError = "文件复制不完整";
        LittleFS.remove(PHOTO_FILE_PATH);
        return false;
    }

    // 更新状态
    currentMetadata = metadata;
    photoExists = true;

    USBSerial.println("原始JPEG文件保存成功");
    return true;
}



bool PhotoGallery::getJPEGDimensions(File& file, uint16_t& width, uint16_t& height) {
    USBSerial.println("获取JPEG尺寸信息");

    size_t fileSize = file.size();

    // 尝试分配较小的缓冲区来读取JPEG头信息
    const size_t HEADER_BUFFER_SIZE = 1024;  // 1KB应该足够读取JPEG头
    uint8_t* headerBuffer = (uint8_t*)malloc(HEADER_BUFFER_SIZE);
    if (!headerBuffer) {
        lastError = "无法分配头部缓冲区";
        USBSerial.println("错误：头部缓冲区分配失败");
        return false;
    }

    // 读取JPEG文件头
    file.seek(0);
    size_t headerRead = file.read(headerBuffer, HEADER_BUFFER_SIZE);
    if (headerRead < 100) {  // JPEG头至少需要几十字节
        free(headerBuffer);
        lastError = "JPEG文件头读取失败";
        USBSerial.println("错误：文件头读取不足");
        return false;
    }

    // 使用TJpg_Decoder获取图片尺寸
    if (TJpgDec.getJpgSize(&width, &height, headerBuffer, headerRead) != JDR_OK) {
        // 如果头部不够，尝试读取更多数据
        free(headerBuffer);

        // 分配更大的缓冲区（但仍然比整个文件小）
        const size_t LARGER_BUFFER_SIZE = min(fileSize, (size_t)8192);  // 最多8KB
        uint8_t* largerBuffer = (uint8_t*)malloc(LARGER_BUFFER_SIZE);
        if (!largerBuffer) {
            lastError = "无法分配扩展缓冲区";
            USBSerial.println("错误：扩展缓冲区分配失败");
            return false;
        }

        file.seek(0);
        size_t largerRead = file.read(largerBuffer, LARGER_BUFFER_SIZE);

        if (TJpgDec.getJpgSize(&width, &height, largerBuffer, largerRead) != JDR_OK) {
            free(largerBuffer);
            lastError = "JPEG文件格式错误";
            USBSerial.println("错误：无法解析JPEG文件");
            return false;
        }

        free(largerBuffer);
    } else {
        free(headerBuffer);
    }

    return true;
}

bool PhotoGallery::decodeAndDisplayJPEG(const String& originalName) {
    USBSerial.println("开始逐行解码显示JPEG");

    // 打开已保存的JPEG文件
    File jpegFile = LittleFS.open(PHOTO_FILE_PATH, "r");
    if (!jpegFile) {
        lastError = "无法打开已保存的JPEG文件";
        return false;
    }

    size_t fileSize = jpegFile.size();
    USBSerial.printf("开始解码JPEG文件，大小: %d字节\n", fileSize);

    // 清屏为黑色背景
    gfx->fillScreen(0x0000);
    USBSerial.println("屏幕已清除为黑色背景");

    // 配置TJpg_Decoder为仅显示模式（不保存）
    TJpgDec.setJpgScale(1);  // 不使用硬件缩放，我们手动处理
    TJpgDec.setCallback(tjpgDisplayOnly);  // 设置仅显示的回调

    // 设置静态变量供回调函数使用
    currentInstance = this;
    currentOutputFile = nullptr;  // 不保存，仅显示
    currentScaleParams = {currentMetadata.displayWidth / (float)currentMetadata.originalWidth,
                         currentMetadata.displayWidth, currentMetadata.displayHeight,
                         currentMetadata.offsetX, currentMetadata.offsetY};

    // 分配JPEG解码缓冲区（这次是从已保存的文件读取）
    uint8_t* jpegBuffer = (uint8_t*)malloc(fileSize);
    if (!jpegBuffer) {
        jpegFile.close();
        lastError = "JPEG解码缓冲区分配失败";
        currentInstance = nullptr;
        USBSerial.printf("无法分配%d字节的JPEG缓冲区\n", fileSize);
        return false;
    }

    // 读取JPEG数据
    jpegFile.seek(0);
    size_t jpegRead = jpegFile.read(jpegBuffer, fileSize);
    jpegFile.close();

    if (jpegRead != fileSize) {
        free(jpegBuffer);
        lastError = "JPEG文件读取不完整";
        currentInstance = nullptr;
        return false;
    }

    USBSerial.println("开始JPEG解码显示...");

    // 执行JPEG解码，仅显示
    JRESULT result = TJpgDec.drawJpg(0, 0, jpegBuffer, jpegRead);

    free(jpegBuffer);

    // 清理静态变量
    currentInstance = nullptr;
    currentOutputFile = nullptr;

    bool success = (result == JDR_OK);
    if (!success) {
        lastError = "JPEG解码失败，错误码: " + String(result);
        USBSerial.printf("JPEG解码失败，错误码: %d\n", result);
    } else {
        USBSerial.println("JPEG解码显示成功");
    }

    return success;
}


bool PhotoGallery::processBMP(File& file, const String& originalName) {
    USBSerial.println("开始处理BMP文件");

    // 简单的BMP处理实现
    // 这里先实现一个基础版本，读取BMP头信息

    // BMP文件头结构
    struct BMPHeader {
        uint16_t signature;      // 'BM'
        uint32_t fileSize;
        uint32_t reserved;
        uint32_t dataOffset;
        uint32_t headerSize;
        uint32_t width;
        uint32_t height;
        uint16_t planes;
        uint16_t bitsPerPixel;
        uint32_t compression;
        uint32_t imageSize;
        uint32_t xPixelsPerMeter;
        uint32_t yPixelsPerMeter;
        uint32_t colorsUsed;
        uint32_t colorsImportant;
    };

    BMPHeader header;

    // 读取BMP头
    if (file.read((uint8_t*)&header, sizeof(BMPHeader)) != sizeof(BMPHeader)) {
        lastError = "BMP文件头读取失败";
        return false;
    }

    // 验证BMP签名
    if (header.signature != 0x4D42) {  // 'BM'
        lastError = "不是有效的BMP文件";
        return false;
    }

    // 检查格式支持
    if (header.bitsPerPixel != 24 && header.bitsPerPixel != 16) {
        lastError = "仅支持16位或24位BMP文件";
        USBSerial.printf("不支持的BMP格式：%d位\n", header.bitsPerPixel);
        return false;
    }

    USBSerial.printf("BMP信息：%dx%d, %d位\n", header.width, header.height, header.bitsPerPixel);

    // 暂时返回成功，实际的像素处理将在后续实现
    lastError = "BMP处理功能正在开发中";
    USBSerial.println("BMP基础解析成功，但像素处理尚未实现");

    return false;  // 暂时返回失败，直到完整实现
}

bool PhotoGallery::tjpgOutput(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap) {
    if (!currentOutputFile || !currentInstance) {
        return false;
    }

    // 获取缩放参数
    ScaleParams& params = currentScaleParams;

    // 使用小行缓冲区，既显示又保存
    static uint16_t* lineBuffer = nullptr;
    static bool bufferInitialized = false;
    static int currentLine = 0;
    static bool isFirstCall = true;

    if (!bufferInitialized) {
        // 分配一行的缓冲区（240像素 x 2字节 = 480字节）
        lineBuffer = (uint16_t*)malloc(SCREEN_SIZE * sizeof(uint16_t));
        if (!lineBuffer) {
            USBSerial.println("错误：无法分配行缓冲区");
            return false;
        }

        bufferInitialized = true;
        currentLine = 0;
        isFirstCall = true;
        USBSerial.println("行缓冲区初始化完成，开始处理JPEG");
    }

    if (isFirstCall) {
        // 第一次调用时清屏为黑色
        gfx->fillScreen(0x0000);
        isFirstCall = false;
        USBSerial.println("开始JPEG处理：显示+保存");
    }

    // 处理当前块的每个像素，既显示又保存
    for (uint16_t dy = 0; dy < h; dy++) {
        for (uint16_t dx = 0; dx < w; dx++) {
            // 原始图片中的坐标
            int16_t srcX = x + dx;
            int16_t srcY = y + dy;

            // 计算缩放后在240x240屏幕中的坐标
            int16_t dstX = (int16_t)(srcX * params.scale) + params.offsetX;
            int16_t dstY = (int16_t)(srcY * params.scale) + params.offsetY;

            // 检查是否在屏幕范围内
            if (dstX >= 0 && dstX < SCREEN_SIZE && dstY >= 0 && dstY < SCREEN_SIZE) {
                uint16_t pixelColor = bitmap[dy * w + dx];

                // 1. 直接绘制到屏幕
                gfx->drawPixel(dstX, dstY, pixelColor);

                // 2. 处理行缓冲区保存
                // 如果是新的一行，先写入上一行
                if (dstY > currentLine) {
                    // 写入当前行到文件
                    while (currentLine < dstY && currentLine < SCREEN_SIZE) {
                        // 检查剩余空间
                        size_t freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
                        if (freeBytes < SCREEN_SIZE * sizeof(uint16_t) + 2048) {  // 预留2KB
                            USBSerial.printf("警告：SPIFFS空间不足，停止保存在行%d\n", currentLine);
                            USBSerial.printf("剩余空间: %d字节，需要: %d字节\n", freeBytes, SCREEN_SIZE * sizeof(uint16_t));
                            // 不返回错误，继续显示但停止保存
                            goto skip_save;
                        }

                        if (currentOutputFile->write((uint8_t*)lineBuffer, SCREEN_SIZE * sizeof(uint16_t)) != SCREEN_SIZE * sizeof(uint16_t)) {
                            USBSerial.printf("错误：写入行%d失败\n", currentLine);
                            return false;
                        }
                        currentLine++;

                        // 清空行缓冲区为下一行准备
                        for (int i = 0; i < SCREEN_SIZE; i++) {
                            lineBuffer[i] = 0x0000;  // 黑色
                        }
                    }
                }

                skip_save:

                // 设置当前行的像素
                if (currentLine == dstY && currentLine < SCREEN_SIZE) {
                    lineBuffer[dstX] = pixelColor;
                }
            }
        }
    }

    // 检查是否是最后一块（简单判断：如果y+h接近原图高度）
    static int16_t maxY = 0;
    if (y + h > maxY) {
        maxY = y + h;
    }

    // 如果处理完成，写入剩余行并重置状态
    if (y + h >= currentScaleParams.dstH / params.scale) {  // 接近原图底部
        USBSerial.println("JPEG处理完成，写入剩余行");

        // 写入剩余的行到文件
        while (currentLine < SCREEN_SIZE) {
            // 检查剩余空间
            size_t freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
            if (freeBytes < SCREEN_SIZE * sizeof(uint16_t) + 1024) {  // 预留1KB
                USBSerial.printf("警告：SPIFFS空间不足，停止保存在行%d\n", currentLine);
                USBSerial.printf("剩余空间: %d字节，需要: %d字节\n", freeBytes, SCREEN_SIZE * sizeof(uint16_t));
                break;  // 停止保存，但不返回错误
            }

            if (currentOutputFile->write((uint8_t*)lineBuffer, SCREEN_SIZE * sizeof(uint16_t)) != SCREEN_SIZE * sizeof(uint16_t)) {
                USBSerial.printf("错误：写入最终行%d失败\n", currentLine);
                return false;
            }
            currentLine++;

            // 清空行缓冲区
            for (int i = 0; i < SCREEN_SIZE; i++) {
                lineBuffer[i] = 0x0000;  // 黑色
            }
        }

        // 清理资源
        free(lineBuffer);
        lineBuffer = nullptr;
        bufferInitialized = false;
        currentLine = 0;
        isFirstCall = true;
        maxY = 0;

        USBSerial.println("照片已显示并保存完成");
    }

    return true;
}

// 强力清理gallery目录
void PhotoGallery::cleanupGalleryDirectory() {
    USBSerial.println("开始强力清理gallery目录");

    // 显示清理前的空间状态
    size_t beforeUsed = LittleFS.usedBytes();
    size_t beforeFree = LittleFS.totalBytes() - LittleFS.usedBytes();
    USBSerial.printf("清理前：已用 %d 字节，可用 %d 字节\n", beforeUsed, beforeFree);

    // 打开gallery目录
    File galleryDir = LittleFS.open("/gallery");
    if (!galleryDir || !galleryDir.isDirectory()) {
        USBSerial.println("gallery目录不存在，尝试创建");
        LittleFS.mkdir("/gallery");
        return;
    }

    // 列出并删除gallery目录中的所有文件
    USBSerial.println("扫描gallery目录中的文件：");
    File file = galleryDir.openNextFile();
    int deletedCount = 0;
    size_t deletedBytes = 0;

    while (file) {
        if (!file.isDirectory()) {
            String fileName = file.name();
            size_t fileSize = file.size();
            file.close();  // 关闭文件句柄

            USBSerial.printf("  发现文件: %s (%d字节)\n", fileName.c_str(), fileSize);

            // 删除文件
            String fullPath = "/gallery/" + fileName;
            if (LittleFS.remove(fullPath)) {
                USBSerial.printf("  ✓ 已删除: %s\n", fileName.c_str());
                deletedCount++;
                deletedBytes += fileSize;
            } else {
                USBSerial.printf("  ✗ 删除失败: %s\n", fileName.c_str());
            }
        } else {
            file.close();
        }

        file = galleryDir.openNextFile();
    }

    galleryDir.close();

    // 显示清理结果
    size_t afterUsed = LittleFS.usedBytes();
    size_t afterFree = LittleFS.totalBytes() - LittleFS.usedBytes();

    USBSerial.printf("清理完成：删除了 %d 个文件，释放了 %d 字节\n", deletedCount, deletedBytes);
    USBSerial.printf("清理后：已用 %d 字节，可用 %d 字节\n", afterUsed, afterFree);
    USBSerial.printf("实际释放空间: %d 字节\n", beforeUsed - afterUsed);

    // 更新状态
    photoExists = false;
    memset(&currentMetadata, 0, sizeof(PhotoMetadata));
}

// 分析LittleFS空间占用情况
void PhotoGallery::analyzeLittleFSUsage() {
    USBSerial.println("=== LittleFS 详细空间分析 ===");

    size_t totalBytes = LittleFS.totalBytes();
    size_t usedBytes = LittleFS.usedBytes();
    size_t freeBytes = totalBytes - usedBytes;

    USBSerial.printf("总空间: %d 字节 (%.2f MB)\n", totalBytes, totalBytes / 1024.0 / 1024.0);
    USBSerial.printf("已用空间: %d 字节 (%.2f MB)\n", usedBytes, usedBytes / 1024.0 / 1024.0);
    USBSerial.printf("可用空间: %d 字节 (%.2f KB)\n", freeBytes, freeBytes / 1024.0);

    // 扫描根目录
    USBSerial.println("\n=== 根目录文件列表 ===");
    File root = LittleFS.open("/");
    if (root && root.isDirectory()) {
        File file = root.openNextFile();
        size_t rootFilesSize = 0;
        int rootFileCount = 0;

        while (file) {
            if (!file.isDirectory()) {
                size_t fileSize = file.size();
                rootFilesSize += fileSize;
                rootFileCount++;
                USBSerial.printf("  %s: %d 字节 (%.1f KB)\n",
                               file.name(), fileSize, fileSize / 1024.0);
            }
            file.close();
            file = root.openNextFile();
        }
        root.close();

        USBSerial.printf("根目录文件总计: %d 个文件，%d 字节 (%.1f KB)\n",
                        rootFileCount, rootFilesSize, rootFilesSize / 1024.0);
    }

    // 扫描gallery目录
    USBSerial.println("\n=== Gallery目录文件列表 ===");
    File galleryDir = LittleFS.open("/gallery");
    if (galleryDir && galleryDir.isDirectory()) {
        File file = galleryDir.openNextFile();
        size_t galleryFilesSize = 0;
        int galleryFileCount = 0;

        while (file) {
            if (!file.isDirectory()) {
                size_t fileSize = file.size();
                galleryFilesSize += fileSize;
                galleryFileCount++;
                USBSerial.printf("  %s: %d 字节 (%.1f KB)\n",
                               file.name(), fileSize, fileSize / 1024.0);
            }
            file.close();
            file = galleryDir.openNextFile();
        }
        galleryDir.close();

        USBSerial.printf("Gallery目录文件总计: %d 个文件，%d 字节 (%.1f KB)\n",
                        galleryFileCount, galleryFilesSize, galleryFilesSize / 1024.0);
    } else {
        USBSerial.println("Gallery目录不存在或无法访问");
    }

    // 计算可上传的最大图片大小
    size_t maxPhotoSize = freeBytes > 20000 ? freeBytes - 20000 : 0;  // 预留20KB
    USBSerial.printf("\n=== 空间容量分析 ===\n");
    USBSerial.printf("当前可上传的最大图片大小: %d 字节 (%.1f KB)\n",
                     maxPhotoSize, maxPhotoSize / 1024.0);

    if (maxPhotoSize < 50000) {
        USBSerial.println("⚠️  警告：可用空间不足50KB，建议清理文件");
    } else if (maxPhotoSize < 100000) {
        USBSerial.println("⚠️  注意：可用空间不足100KB，只能上传小图片");
    } else if (maxPhotoSize < 100000) {
        USBSerial.printf("✅ 可以上传最大 %.0f KB 的图片\n", maxPhotoSize / 1024.0);
    } else {
        USBSerial.println("✅ 空间充足，可以上传100KB以内的图片");
    }

    USBSerial.println("=== 空间分析完成 ===\n");
}

// 仅显示的JPEG解码回调函数（不保存）
bool PhotoGallery::tjpgDisplayOnly(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap) {
    if (!currentInstance) {
        return false;
    }

    // 获取缩放参数
    ScaleParams& params = currentScaleParams;

    static bool isFirstCall = true;
    if (isFirstCall) {
        USBSerial.println("开始JPEG逐行显示（仅显示模式）");
        isFirstCall = false;
    }

    // 处理当前块的每个像素，仅显示不保存
    for (uint16_t dy = 0; dy < h; dy++) {
        for (uint16_t dx = 0; dx < w; dx++) {
            // 原始图片中的坐标
            int16_t srcX = x + dx;
            int16_t srcY = y + dy;

            // 计算缩放后在240x240屏幕中的坐标
            int16_t dstX = (int16_t)(srcX * params.scale) + params.offsetX;
            int16_t dstY = (int16_t)(srcY * params.scale) + params.offsetY;

            // 检查是否在屏幕范围内
            if (dstX >= 0 && dstX < SCREEN_SIZE && dstY >= 0 && dstY < SCREEN_SIZE) {
                uint16_t pixelColor = bitmap[dy * w + dx];

                // 直接绘制到屏幕（逐像素扫描线方式）
                gfx->drawPixel(dstX, dstY, pixelColor);
            }
        }
    }

    // 检查是否处理完成
    static int16_t maxY = 0;
    if (y + h > maxY) {
        maxY = y + h;
    }

    // 如果处理完成，重置状态
    if (y + h >= currentScaleParams.dstH / params.scale) {  // 接近原图底部
        USBSerial.println("JPEG逐行显示完成");
        isFirstCall = true;
        maxY = 0;
    }

    return true;
}
