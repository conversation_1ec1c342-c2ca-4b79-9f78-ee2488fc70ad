#include <Arduino.h>
#include "Arduino_GFX_Library.h"
#include "LittleFS.h"
#include "pin_config.h"
#include "mario_theme.h"
#include "mario_assets.h"
#include "boot_screen.h"
#include "wifi_manager.h"
#include "ntp_auto_sync.h"
#include "stock_monitor.h"
#include "photo_gallery.h"

HWCDC USBSerial;

// 屏幕及颜色定义
#define SCREEN_WIDTH 240
#define SCREEN_HEIGHT 240
#define FC_DARK_RED 0x8020
#define FC_GOLDEN 0xB420

// 虚拟显示区域（192x192）
const int VIRTUAL_WIDTH = 192;
const int VIRTUAL_HEIGHT = 192;
const int OFFSET_X = (SCREEN_WIDTH - VIRTUAL_WIDTH) / 2;
const int OFFSET_Y = (SCREEN_HEIGHT - VIRTUAL_HEIGHT) / 2;

// 显示驱动实例
Arduino_DataBus *bus = new Arduino_ESP32SPI(LCD_DC, -1 /* CS接地 */, LCD_SCK, LCD_MOSI, -1 /* MISO */, 80000000 /* 80MHz */);
Arduino_GFX *gfx = new Arduino_ST7789(bus, LCD_RST /* RST */, 0 /* rotation */, true /* IPS */, LCD_WIDTH, LCD_HEIGHT, 0, 0, 0, 0);

// 双缓冲相关函数外部声明（这些函数应在 mario_theme.h 中定义或在此文件中实现）
extern bool useFrameBuffer;
extern bool frameBufferDirty;
extern bool initFrameBuffer();
extern void renderFrameBuffer();

// 虚拟绘图函数实现
void drawPixelVirtual(int16_t x, int16_t y, uint16_t color) {
    if (x >= 0 && x < VIRTUAL_WIDTH && y >= 0 && y < VIRTUAL_HEIGHT) {
        gfx->drawPixel(OFFSET_X + x, OFFSET_Y + y, color);
    }
}

void fillRectVirtual(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
    if (x < VIRTUAL_WIDTH && y < VIRTUAL_HEIGHT && x + w > 0 && y + h > 0) {
        int16_t x1 = (x > 0) ? x : 0;
        int16_t y1 = (y > 0) ? y : 0;
        int16_t x2 = (x + w < VIRTUAL_WIDTH) ? (x + w) : VIRTUAL_WIDTH;
        int16_t y2 = (y + h < VIRTUAL_HEIGHT) ? (y + h) : VIRTUAL_HEIGHT;

        gfx->fillRect(OFFSET_X + x1, OFFSET_Y + y1, x2 - x1, y2 - y1, color);
    }
}

// drawTextToFrameBuffer 函数在 mario_theme.cpp 中已定义，这里不需要重复定义

// 绘制边框和背景的辅助函数
void drawVirtualScreenBorder() {
    gfx->fillRect(OFFSET_X - 4, OFFSET_Y - 4, VIRTUAL_WIDTH + 8, 4, FC_DARK_RED);
    gfx->fillRect(OFFSET_X - 4, OFFSET_Y + VIRTUAL_HEIGHT, VIRTUAL_WIDTH + 8, 4, FC_DARK_RED);
    gfx->fillRect(OFFSET_X - 4, OFFSET_Y, 4, VIRTUAL_HEIGHT, FC_DARK_RED);
    gfx->fillRect(OFFSET_X + VIRTUAL_WIDTH, OFFSET_Y, 4, VIRTUAL_HEIGHT, FC_DARK_RED);
}

void fillBackgroundAreas() {
    gfx->fillRect(0, 0, SCREEN_WIDTH, OFFSET_Y - 4, FC_GOLDEN);
    gfx->fillRect(0, OFFSET_Y + VIRTUAL_HEIGHT + 4, SCREEN_WIDTH, SCREEN_HEIGHT - (OFFSET_Y + VIRTUAL_HEIGHT + 4), FC_GOLDEN);
    gfx->fillRect(0, OFFSET_Y - 4, OFFSET_X - 4, VIRTUAL_HEIGHT + 8, FC_GOLDEN);
    gfx->fillRect(OFFSET_X + VIRTUAL_WIDTH + 4, OFFSET_Y - 4, SCREEN_WIDTH - (OFFSET_X + VIRTUAL_WIDTH + 4), VIRTUAL_HEIGHT + 8, FC_GOLDEN);
}

// 马里奥时钟核心对象
Scene scene;
Mario mario;
Block hourBlock(HOUR_BLOCK_X, HOUR_BLOCK_Y);
Block minuteBlock(MIN_BLOCK_X, MIN_BLOCK_Y);
// 栗子精灵 - 放置在原版项目的起始位置（-16*3=-48, 40*3=120）
Goomba goomba(-48, 120);
// 动画精灵（蘑菇和刺猬交替出现） - 放置在原版项目的起始位置（43*3=129, 40*3=120）
AnimatedSprite animatedSprite(129, 120);

// WiFi管理对象
WifiManager wifiMgr;
NTPAutoSync ntpAutoSync;

// 股票监控对象
StockMonitor stockMonitor;

// 相册对象
PhotoGallery photoGallery;

// 背光控制函数 - P沟道MOSFET，低电平有效
void setBacklight(bool enable) {
    digitalWrite(LCD_BL, enable ? LOW : HIGH);  // 低电平=开，高电平=关
}

void setBacklightBrightness(uint8_t brightness) {
    // 对于P沟道MOSFET，需要反转PWM值
    analogWrite(LCD_BL, 255 - brightness);  // 反转亮度值
}

// LittleFS文件系统初始化函数
bool initLittleFS() {
    USBSerial.println();
    USBSerial.println("=== Initializing LittleFS ===");
    USBSerial.flush();

    if (!LittleFS.begin(true)) {  // true表示格式化失败时自动格式化
        USBSerial.println("ERROR: LittleFS initialization failed!");
        USBSerial.flush();
        return false;
    }

    // 显示文件系统信息
    size_t totalBytes = LittleFS.totalBytes();
    size_t usedBytes = LittleFS.usedBytes();
    size_t freeBytes = totalBytes - usedBytes;

    USBSerial.printf("LittleFS initialized successfully\n");
    USBSerial.printf("Total space: %d bytes (%.2f MB)\n", totalBytes, totalBytes / 1024.0 / 1024.0);
    USBSerial.printf("Used space: %d bytes (%.2f MB)\n", usedBytes, usedBytes / 1024.0 / 1024.0);
    USBSerial.printf("Free space: %d bytes (%.2f MB)\n", freeBytes, freeBytes / 1024.0 / 1024.0);

    // 检查是否使用了新的2MB分区
    if (totalBytes < 1500000) {  // 小于1.5MB
        USBSerial.println("⚠️  警告：LittleFS空间小于1.5MB，可能还在使用旧分区");
        USBSerial.println("   建议：确认已选择'2MB APP (No OTA)/2MB SPIFFS'分区表");
        USBSerial.println("   然后重新烧录程序以应用新分区");
    } else {
        USBSerial.println("✅ 检测到2MB SPIFFS分区，空间充足");
    }

    // 创建gallery目录（如果不存在）
    if (!LittleFS.exists("/gallery")) {
        if (LittleFS.mkdir("/gallery")) {
            USBSerial.println("Created /gallery directory");
        } else {
            USBSerial.println("Failed to create /gallery directory");
            return false;
        }
    } else {
        USBSerial.println("/gallery directory already exists");
    }

    return true;
}

// LittleFS基础功能测试
void testLittleFS() {
    USBSerial.println("=== Testing LittleFS Basic Functions ===");

    // 测试文件写入
    File testFile = LittleFS.open("/gallery/test.txt", "w");
    if (testFile) {
        testFile.println("LittleFS test file");
        testFile.println("Gallery module initialization test");
        testFile.close();
        USBSerial.println("Test file created successfully");
    } else {
        USBSerial.println("Failed to create test file");
        return;
    }

    // 测试文件读取
    testFile = LittleFS.open("/gallery/test.txt", "r");
    if (testFile) {
        USBSerial.println("Test file content:");
        while (testFile.available()) {
            USBSerial.write(testFile.read());
        }
        testFile.close();
        USBSerial.println("Test file read successfully");
    } else {
        USBSerial.println("Failed to read test file");
        return;
    }

    // 测试文件删除
    if (LittleFS.remove("/gallery/test.txt")) {
        USBSerial.println("Test file deleted successfully");
    } else {
        USBSerial.println("Failed to delete test file");
    }

    USBSerial.println("LittleFS basic functions test completed");
}

// 全局变量定义
bool isRealTimeUpdate = false;  // 时间更新标志

// 硬编码时间及更新逻辑
int currentHour = 10;
int currentMinute = 8;
int lastHour = 10;      // 添加缺失的变量
int lastMinute = 8;     // 添加缺失的变量
unsigned long lastTimeUpdate = 0;
unsigned long lastMarioJump = 0;
#define TIME_UPDATE_INTERVAL 60000  // 60秒
#define MARIO_JUMP_INTERVAL 5000    // 5秒

// 模式切换控制变量
bool galleryDisplayInitialized = false;
bool stockDisplayInitialized = false;
bool firstDataFetch = false;

void updateTimeDisplay() {
    char hourStr[3];
    char minuteStr[3];
    sprintf(hourStr, "%d", currentHour);
    sprintf(minuteStr, "%02d", currentMinute);
    hourBlock.setText(String(hourStr));
    minuteBlock.setText(String(minuteStr));
}

// 模式切换回调函数
void onModeChange() {
    USBSerial.println("=== 模式切换回调触发 ===");

    // 重置所有模式的初始化状态
    galleryDisplayInitialized = false;
    stockDisplayInitialized = false;
    firstDataFetch = false;

    // 清理当前模式的资源
    if (wifiMgr.getGalleryModeEnabled()) {
        USBSerial.println("切换到相册模式");

        // 清理其他模块
        cleanupMarioFrameBuffer();
        stockMonitor.cleanup();

        // 初始化相册模块
        photoGallery.init();

        // 立即显示相册内容
        photoGallery.display();

        USBSerial.println("相册模块初始化完成");
    } else if (wifiMgr.getStockModeEnabled()) {
        USBSerial.println("切换到股票模式");

        // 清理其他模块
        cleanupMarioFrameBuffer();
        // 相册模块不需要特殊清理

        // 初始化股票监控模块
        stockMonitor.init(wifiMgr.stockCodes);

        // 清屏并首次显示
        stockMonitor.clearScreen();
        stockMonitor.display();

        USBSerial.println("股票监控模块初始化完成");
    } else {
        USBSerial.println("切换到马里奥时钟模式");

        // 清理其他模块
        stockMonitor.cleanup();
        // 相册模块不需要特殊清理

        // 清屏并重新绘制马里奥时钟界面
        gfx->fillScreen(BLACK);

        // 重新初始化双缓冲（如果需要）
        if (!initFrameBuffer()) {
            USBSerial.println("Framebuffer init failed, using direct drawing.");
        }

        // 绘制背景和边框
        fillBackgroundAreas();
        drawVirtualScreenBorder();
        drawCornerSquares();

        // 重新初始化场景和游戏对象
        extern Scene scene;
        extern Block hourBlock;
        extern Block minuteBlock;
        extern Mario mario;
        extern Goomba goomba;
        extern AnimatedSprite animatedSprite;

        // 重新初始化场景
        scene.init();

        // 重新初始化时间方块
        hourBlock.init();
        minuteBlock.init();

        // 重新初始化马里奥
        mario.init();

        // 重新初始化其他游戏对象
        goomba.init();
        animatedSprite.init();

        // 更新时间显示
        updateTimeDisplay();

        // 首次绘制所有马里奥时钟元素
        scene.draw();
        hourBlock.draw();
        minuteBlock.draw();
        mario.draw();
        goomba.draw();
        animatedSprite.draw();

        if (useFrameBuffer) {
            renderFrameBuffer();
        }

        USBSerial.println("马里奥时钟模式初始化完成");
    }

    USBSerial.println("=== 模式切换完成 ===");
}

void setup(void) {
    USBSerial.begin(115200);
    delay(1000);  // 等待串口稳定

    USBSerial.println();
    USBSerial.println("===========================================");
    USBSerial.println("Mario Clock Standalone Starting...");
    USBSerial.println("===========================================");
    USBSerial.flush();  // 确保数据发送完成

    // 初始化LittleFS文件系统
    if (!initLittleFS()) {
        USBSerial.println("LittleFS initialization failed, continuing without file system...");
    } else {
        // 测试LittleFS基础功能
        testLittleFS();
    }

    // 初始化显示
    USBSerial.println("Initializing display...");
    if (!gfx->begin()) {
        USBSerial.println("gfx->begin() failed!");
    } else {
        USBSerial.println("gfx->begin() successful!");
    }

    // 初始化背光控制 - P沟道MOSFET，低电平有效
    pinMode(LCD_BL, OUTPUT);
    setBacklightBrightness(255);  // 设置亮度为200（0-255范围）

    gfx->fillScreen(BLACK);

    // 初始化双缓冲
    if (!initFrameBuffer()) {
        USBSerial.println("Framebuffer init failed, using direct drawing.");
    } else {
        USBSerial.println("Framebuffer init successful.");
    }

    // 显示开机画面并完成初始化
    BootScreen bootScreen;

    // 先显示开机画面的静态部分
    bootScreen.drawInitialScreen();
    bootScreen.updateCountdown(3);

    // 完成所有初始化工作
    USBSerial.println("Starting complete initialization...");

    bootScreen.updateStatus("LOADING SETTINGS...");
    delay(200);

    // 初始化WiFi管理器（快速启动AP和Web服务器）
    wifiMgr.setPhotoGallery(&photoGallery);
    wifiMgr.init();  // 使用新的init()方法，包含异步WiFi连接

    bootScreen.updateStatus("WIFI MANAGER READY");
    delay(200);

    // 网络连接现在是异步的，不需要在启动时检查失败状态

    // 根据离线模式显示状态
    if (wifiMgr.offlineModeEnabled) {
        bootScreen.updateStatus("OFFLINE MODE ENABLED");
        delay(400);
    } else {
        // WiFi连接现在是异步的，显示正在连接状态
        bootScreen.updateStatus("WIFI CONNECTING...");
        delay(400);

        // 注意：实际的WiFi连接状态将在后台处理
        // 用户可以通过Web界面查看连接状态
    }

    bootScreen.updateStatus("STARTING SERVICES...");
    delay(200);

    // 初始化NTP自动同步模块
    ntpAutoSync.init(wifiMgr.getOfflineModePtr(), wifiMgr.getNTPServer(), wifiMgr.getTimezone());

    // 设置NTP自动同步配置更新回调
    wifiMgr.setNTPAutoSyncCallback([](const String& server, const String& timezone) {
        ntpAutoSync.setNTPServer(server);
        ntpAutoSync.setTimezone(timezone);
    });

    // 设置模式切换回调
    wifiMgr.setModeChangeCallback(onModeChange);

    // 标记WiFi管理器初始化完成
    wifiMgr.initializationComplete = true;

    bootScreen.updateStatus("READY");
    delay(500);

    // 清除状态文字，准备开始倒计时
    bootScreen.updateStatus("");
    delay(300);

    // 现在开始正常的倒计时：X 3 → X 2 → X 1
    USBSerial.println("Starting final countdown...");
    for (int countdown = 3; countdown >= 1; countdown--) {
        USBSerial.print("Final countdown: ");
        USBSerial.println(countdown);

        bootScreen.updateCountdown(countdown);
        delay(1000);
    }

    // 网络连接现在是异步的，不在启动时显示网络错误
    // 用户可以通过Web界面查看实际的连接状态

    USBSerial.println("Boot screen completed");

    // 检查模式状态并初始化相应模块
    if (wifiMgr.getGalleryModeEnabled()) {
        USBSerial.println("相册模式已开启，使用扫描线模式初始化相册模块");

        // 扫描线模式内存需求很小，可以与其他模块共存
        // 但为了安全起见，仍然释放其他模块的大缓冲区
        cleanupMarioFrameBuffer();
        stockMonitor.cleanup();

        // 初始化相册模块
        photoGallery.init();

        USBSerial.println("相册模块初始化完成");
    } else if (wifiMgr.getStockModeEnabled()) {
        USBSerial.println("股票模式已开启，初始化股票监控模块");

        // 初始化股票监控模块
        stockMonitor.init(wifiMgr.stockCodes);

        USBSerial.println("股票监控模块初始化完成");
    } else {
        USBSerial.println("股票模式和相册模式已关闭，使用马里奥时钟模式");

        // 绘制背景和边框（只在马里奥时钟模式下）
        fillBackgroundAreas();
        drawVirtualScreenBorder();
        drawCornerSquares(); // 假设此函数在 mario_theme.h 中定义

        if (useFrameBuffer) {
            renderFrameBuffer();
        }

        // 初始化马里奥时钟相关对象（保持原有逻辑）
        scene.init();
        updateTimeDisplay();
        hourBlock.init();
        minuteBlock.init();
        mario.init();
        goomba.init();
        animatedSprite.init();

        // 首次绘制所有马里奥时钟元素
        scene.draw();
        hourBlock.draw();
        minuteBlock.draw();
        mario.draw();
        goomba.draw();
        animatedSprite.draw();

        USBSerial.println("马里奥时钟模式初始化完成");
    }

    USBSerial.println("Mario Clock setup complete.");
}

void loop() {
    unsigned long currentTime = millis();
    static unsigned long lastFrameTime = 0;

    // 处理WiFi客户端请求
    wifiMgr.handleClient();

    // 处理NTP自动同步
    ntpAutoSync.update();

    if (wifiMgr.getGalleryModeEnabled()) {
        // 相册模式循环
        // 初始化显示（只执行一次）
        if (!galleryDisplayInitialized) {
            photoGallery.display();
            galleryDisplayInitialized = true;
        }

        // 相册模式更新（静态显示，无需频繁更新）
        photoGallery.update();

        delay(1000); // 相册模式更新频率很低
    } else if (wifiMgr.getStockModeEnabled()) {
        // 股票模式循环

        // 首次获取数据（启动后立即尝试）
        if (!firstDataFetch) {
            USBSerial.println("首次获取股票数据...");
            stockMonitor.update();
            firstDataFetch = true;
        }

        // 初始化显示（只执行一次）
        if (!stockDisplayInitialized) {
            stockMonitor.clearScreen(); // 首次清屏
            stockMonitor.resetFirstDisplay(); // 重置首次显示标志
            stockDisplayInitialized = true;
        }

        // 股票模块自主管理更新时机
        stockMonitor.update();    // 内部判断是否需要网络更新
        stockMonitor.display();   // 总是刷新显示（用于滚动效果）

        delay(1000); // 1秒刷新一次显示
    } else {
        // 马里奥时钟模式循环（保持原有逻辑）
        bool timeChanged = false;

        // 定期让马里奥跳跃
        if (currentTime - lastMarioJump >= MARIO_JUMP_INTERVAL) {
            mario.jump();
            lastMarioJump = currentTime;
        }

        // 定期更新时间（硬编码）
        if (currentTime - lastTimeUpdate >= TIME_UPDATE_INTERVAL) {
            currentMinute++;
            if (currentMinute >= 60) {
                currentMinute = 0;
                currentHour++;
                if (currentHour >= 24) {
                    currentHour = 0;
                }
            }
            updateTimeDisplay();
            timeChanged = true;

            // 时间更新时也触发跳跃
            mario.jump();
            lastTimeUpdate = currentTime;
            lastMarioJump = currentTime; // 同步跳跃定时器
        }

        // 更新所有时钟元素的状态
        scene.update();
        hourBlock.update();
        minuteBlock.update();
        mario.update();
        goomba.update();          // 更新栗子精灵动画
        animatedSprite.update();  // 更新动画精灵（蘑菇和刺猬交替）

        // 渲染控制
        bool shouldRender = false;
        if (mario.jumping() || timeChanged) {
            // 马里奥跳跃或时间变化时，应立即渲染
            shouldRender = true;
        } else if (currentTime - lastFrameTime >= 100) { // 空闲时降低帧率
            shouldRender = true;
        }

        if (shouldRender && useFrameBuffer && frameBufferDirty) {
            renderFrameBuffer();
            lastFrameTime = currentTime;
        }

        delay(5); // 稍微延时，给其他任务（如果有）一些CPU时间
    }
}

