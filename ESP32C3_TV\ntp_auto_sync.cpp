#include "ntp_auto_sync.h"

// 外部声明
extern HWCDC USBSerial;
extern int currentHour;
extern int currentMinute;

NTPAutoSync::NTPAutoSync() {
    lastSyncAttempt = 0;
    lastSyncHour = -1;
    syncInProgress = false;
    ntpServer = "ntp.aliyun.com";
    timezone = "UTC+8";
    timezoneOffset = 8 * 3600;
    offlineModeEnabled = nullptr;
}

void NTPAutoSync::init(bool* offlineMode, const String& server, const String& tz) {
    offlineModeEnabled = offlineMode;
    ntpServer = server;
    timezone = tz;
    calculateTimezoneOffset();
    
    USBSerial.println("=== NTP Auto Sync Module Initialized ===");
    USBSerial.print("NTP Server: ");
    USBSerial.println(ntpServer);
    USBSerial.print("Timezone: ");
    USBSerial.println(timezone);
    USBSerial.print("Timezone Offset: ");
    USBSerial.print(timezoneOffset / 3600);
    USBSerial.println(" hours");
}

void NTPAutoSync::update() {
    // 检查是否需要进行NTP同步
    if (shouldSyncNow()) {
        USBSerial.println("[NTP Auto Sync] Starting hourly NTP synchronization...");
        
        syncInProgress = true;
        bool success = performNTPSync();
        syncInProgress = false;
        
        if (success) {
            USBSerial.println("[NTP Auto Sync] Hourly synchronization completed successfully");
            lastSyncAttempt = millis();
            lastSyncHour = currentHour;
        } else {
            USBSerial.println("[NTP Auto Sync] Hourly synchronization failed, will retry next hour");
            lastSyncAttempt = millis();
            // 不更新 lastSyncHour，这样下个整点还会尝试
        }
    }
}

bool NTPAutoSync::shouldSyncNow() {
    // 如果正在同步中，不要重复同步
    if (syncInProgress) {
        return false;
    }
    
    // 检查WiFi连接状态
    if (!isWiFiConnected()) {
        return false;
    }
    
    // 检查离线模式状态
    if (isOfflineModeEnabled()) {
        return false;
    }
    
    // 检查是否为整点时刻
    struct tm timeinfo;
    if (!getLocalTime(&timeinfo)) {
        return false;
    }
    
    int currentHourFromSystem = timeinfo.tm_hour;
    int currentMinuteFromSystem = timeinfo.tm_min;
    
    // 只在整点的前2分钟内尝试同步，避免重复同步
    if (currentMinuteFromSystem > 2) {
        return false;
    }
    
    // 检查是否已经在这个小时同步过了
    if (lastSyncHour == currentHourFromSystem) {
        return false;
    }
    
    // 防止过于频繁的同步尝试（至少间隔5分钟）
    if (millis() - lastSyncAttempt < 5 * 60 * 1000) {
        return false;
    }
    
    return true;
}

bool NTPAutoSync::performNTPSync() {
    // 检查WiFi连接状态
    if (!isWiFiConnected()) {
        USBSerial.println("[NTP Auto Sync] WiFi not connected, sync failed");
        return false;
    }
    
    USBSerial.print("[NTP Auto Sync] Syncing with server: ");
    USBSerial.println(ntpServer);
    
    // 配置NTP
    configTime(timezoneOffset, 0, ntpServer.c_str());
    
    // 等待时间同步，最多等待10秒
    int attempts = 0;
    time_t now = 0;
    while (now < 1000000000 && attempts < 20) {
        // 在同步过程中检查WiFi状态
        if (!isWiFiConnected()) {
            USBSerial.println("[NTP Auto Sync] WiFi disconnected during sync");
            return false;
        }
        
        delay(500);
        time(&now);
        attempts++;
        USBSerial.print(".");
    }
    
    if (now > 1000000000) {
        // 同步成功，更新全局时间变量
        struct tm timeinfo;
        if (getLocalTime(&timeinfo)) {
            currentHour = timeinfo.tm_hour;
            currentMinute = timeinfo.tm_min;
            
            USBSerial.println();
            USBSerial.print("[NTP Auto Sync] Time synchronized: ");
            USBSerial.print(currentHour);
            USBSerial.print(":");
            USBSerial.print(currentMinute < 10 ? "0" : "");
            USBSerial.println(currentMinute);
            
            return true;
        }
    }
    
    USBSerial.println();
    USBSerial.println("[NTP Auto Sync] Time synchronization failed");
    return false;
}

bool NTPAutoSync::isWiFiConnected() {
    return WiFi.status() == WL_CONNECTED;
}

bool NTPAutoSync::isOfflineModeEnabled() {
    return offlineModeEnabled != nullptr && *offlineModeEnabled;
}

void NTPAutoSync::calculateTimezoneOffset() {
    timezoneOffset = 0;
    if (timezone.startsWith("UTC+")) {
        timezoneOffset = timezone.substring(4).toInt() * 3600;
    } else if (timezone.startsWith("UTC-")) {
        timezoneOffset = -timezone.substring(4).toInt() * 3600;
    }
}

void NTPAutoSync::setNTPServer(const String& server) {
    ntpServer = server;
    USBSerial.print("[NTP Auto Sync] NTP server updated to: ");
    USBSerial.println(ntpServer);
}

void NTPAutoSync::setTimezone(const String& tz) {
    timezone = tz;
    calculateTimezoneOffset();
    USBSerial.print("[NTP Auto Sync] Timezone updated to: ");
    USBSerial.print(timezone);
    USBSerial.print(" (offset: ");
    USBSerial.print(timezoneOffset / 3600);
    USBSerial.println(" hours)");
}

bool NTPAutoSync::isSyncInProgress() {
    return syncInProgress;
}

unsigned long NTPAutoSync::getLastSyncTime() {
    return lastSyncAttempt;
}
