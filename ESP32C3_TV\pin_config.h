/*
 * pin_config.h
 * ESP32-C3 TV的引脚定义
 * 根据提供的设备引脚表更新
 */

#ifndef PIN_CONFIG_H
#define PIN_CONFIG_H

// 屏幕引脚定义 - 根据原理图修正
#define LCD_DC   2  // IO2 → LCD_DC (数据/命令选择)
// #define LCD_CS   此项目中接地
#define LCD_SCK  3  // IO3 → LCD_SDC (SPI时钟)
#define LCD_MOSI 5  // IO5 → LCD_SDA (SPI数据)
#define LCD_RST  6  // IO6 → LCD_RST (复位)
#define LCD_BL   1  // IO1 → LCD_BL (背光控制)

// 屏幕尺寸 - ST7789 240x240的尺寸
#define LCD_WIDTH  240
#define LCD_HEIGHT 240

/*
需要注意的特殊点:
片选 (Chip Select / CS): 在本项目中，LCD模块U5的CS引脚（Pin 8）直接接地了。这意味着LCD设备在SPI总线上始终被选中。在编写代码时，您不需要为CS分配一个GPIO引脚来控制它，这在只有单个SPI设备的系统中是很常见的做法。
背光控制逻辑: 背光是通过一个P沟道MOSFET (Q1) 控制的，这是一个高边开关。因此，控制逻辑是反向的（低电平有效）：
将 IO1 设置为 LOW (低电平)，背光亮。
将 IO1 设置为 HIGH (高电平)，背光灭。
*/
#endif // PIN_CONFIG_H