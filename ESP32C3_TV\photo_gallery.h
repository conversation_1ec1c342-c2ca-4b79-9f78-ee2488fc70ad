#ifndef PHOTO_GALLERY_H
#define PHOTO_GALLERY_H

#include <Arduino.h>
#include "LittleFS.h"
#include "Arduino_GFX_Library.h"
#include <TJpg_Decoder.h>

// 照片元数据结构
struct PhotoMetadata {
    uint16_t originalWidth;      // 原始宽度
    uint16_t originalHeight;     // 原始高度
    uint16_t displayWidth;       // 显示宽度
    uint16_t displayHeight;      // 显示高度
    uint16_t offsetX;            // X偏移（居中）
    uint16_t offsetY;            // Y偏移（居中）
    uint32_t fileSize;           // 文件大小
    char uploadTime[20];         // 上传时间
    char originalName[64];       // 原始文件名
};

// 缩放参数结构
struct ScaleParams {
    float scale;                 // 缩放比例
    uint16_t dstW;              // 目标宽度
    uint16_t dstH;              // 目标高度
    uint16_t offsetX;           // X偏移
    uint16_t offsetY;           // Y偏移
};

// 相册模块类
class PhotoGallery {
public:
    PhotoGallery();
    ~PhotoGallery();

    // 初始化和配置
    void init();
    void display();
    void update();

    // 照片管理
    bool savePhoto(const uint8_t* data, size_t size, uint16_t originalWidth, uint16_t originalHeight, const String& originalName);
    bool hasPhoto();
    void deletePhoto();
    bool loadPhotoMetadata();

    // 状态查询
    PhotoMetadata getPhotoMetadata() { return currentMetadata; }
    String getLastError() { return lastError; }

    // 图片处理（公有接口）
    bool processImageFile(const String& tempFilePath, const String& originalName);

private:
    // 照片元数据
    PhotoMetadata currentMetadata;
    bool photoExists;
    String lastError;

    // 双缓冲系统（预留接口）
    uint16_t* galleryFrameBuffer;
    bool useFrameBuffer;
    bool initGalleryFrameBuffer();
    void renderGalleryFrameBuffer();
    void clearGalleryFrameBuffer();

    // 照片处理
    bool loadPhotoFromStorage();
    void drawPhoto();
    void drawNoPhotoMessage();
    ScaleParams calculateScale(uint16_t srcW, uint16_t srcH);

    // 文件系统操作
    bool savePhotoData(const uint8_t* data, size_t size);
    bool savePhotoMetadata(const PhotoMetadata& metadata);
    bool loadPhotoData();

    // 图片处理（私有方法）
    bool processJPEG(File& file, const String& originalName);
    bool processBMP(File& file, const String& originalName);

    // 新的JPEG处理方法（先保存后解码）
    bool saveOriginalJPEG(File& file, const String& originalName);
    bool decodeAndDisplayJPEG(const String& originalName);
    bool getJPEGDimensions(File& file, uint16_t& width, uint16_t& height);

    // 空间清理方法
    void cleanupGalleryDirectory();
    void analyzeLittleFSUsage();

    // JPEG解码相关
    static bool tjpgOutput(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap);
    static bool tjpgDisplayOnly(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap);
    static PhotoGallery* currentInstance;  // 用于回调函数访问实例
    static File* currentOutputFile;        // 当前输出文件
    static ScaleParams currentScaleParams; // 当前缩放参数

    // 常量定义
    static const char* PHOTO_FILE_PATH;
    static const char* PHOTO_INFO_PATH;
    static const uint16_t SCREEN_SIZE = 240;
};

// 外部声明
extern Arduino_GFX *gfx;
extern HWCDC USBSerial;

#endif // PHOTO_GALLERY_H
