#ifndef NTP_AUTO_SYNC_H
#define NTP_AUTO_SYNC_H

#include <Arduino.h>
#include <WiFi.h>
#include <time.h>

class NTPAutoSync {
private:
    unsigned long lastSyncAttempt;
    int lastSyncHour;
    bool syncInProgress;
    String ntpServer;
    String timezone;
    int timezoneOffset;
    
    // 外部依赖的引用
    bool* offlineModeEnabled;
    
    // 私有方法
    bool isWiFiConnected();
    bool isOfflineModeEnabled();
    bool shouldSyncNow();
    bool performNTPSync();
    void calculateTimezoneOffset();
    
public:
    NTPAutoSync();
    
    // 初始化方法
    void init(bool* offlineMode, const String& server, const String& tz);
    
    // 主要方法
    void update();  // 在主循环中调用
    
    // 配置方法
    void setNTPServer(const String& server);
    void setTimezone(const String& tz);
    
    // 状态查询方法
    bool isSyncInProgress();
    unsigned long getLastSyncTime();
};

#endif
