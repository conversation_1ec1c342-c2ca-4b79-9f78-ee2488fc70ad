#ifndef BOOT_SCREEN_H
#define BOOT_SCREEN_H

#include <Arduino.h>

// 8x8像素字体定义（类似马里奥游戏风格）
extern const uint8_t FONT_8X8[][8];

// 开机画面模块
class BootScreen {
public:
    BootScreen();
    void show();  // 显示开机画面，持续3秒
    void showWithInit(void (*initCallback)(int countdown, bool* networkFailed, void (*statusCallback)(const char*)));  // 显示开机画面并执行初始化
    void updateStatus(const char* status);  // 更新底部状态文字（公有方法，供静态函数调用）
    void drawInitialScreen();  // 绘制初始屏幕（标题和马里奥）- 全屏240x240
    void updateCountdown(int countdown);  // 只更新倒计时区域
    void showNetworkError();  // 显示网络错误信息

private:
    void drawText(const char* text, int16_t x, int16_t y, uint16_t color);  // 绘制8x8字体文本（2倍缩放）
    void drawText(const char* text, int16_t x, int16_t y, uint16_t color, int scale);  // 绘制8x8字体文本（指定缩放）
    void drawChar(char c, int16_t x, int16_t y, uint16_t color);  // 绘制单个字符（2倍缩放）
    void drawChar(char c, int16_t x, int16_t y, uint16_t color, int scale);  // 绘制单个字符（指定缩放）
    void calculateCenterPosition(int16_t& marioX, int16_t& textX, int16_t& textY);  // 计算居中位置 - 全屏240x240
    void clearCountdownArea(int16_t x, int16_t y);  // 清除倒计时区域
    void clearStatus();  // 清除底部状态文字
    void drawTextDirect(const char* text, int16_t x, int16_t y, uint16_t color, int scale);  // 直接绘制到屏幕（全屏240x240）
    void drawCharDirect(char c, int16_t x, int16_t y, uint16_t color, int scale);  // 直接绘制字符到屏幕
};

#endif // BOOT_SCREEN_H
