#include "boot_screen.h"
#include "mario_assets.h"
#include "Arduino_GFX_Library.h"

// 外部声明
extern void drawPixelVirtual(int16_t x, int16_t y, uint16_t color);
extern void fillRectVirtual(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
extern Arduino_GFX *gfx;  // 全屏绘制需要直接使用gfx
extern HWCDC USBSerial;

// 8x8像素字体定义（完整的ASCII字符集，马里奥游戏风格）
const uint8_t FONT_8X8[][8] = {
    // 空格 (32)
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    // ! (33)
    {0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00},
    // " (34)
    {0x36, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    // # (35)
    {0x36, 0x36, 0x7F, 0x36, 0x7F, 0x36, 0x36, 0x00},
    // $ (36)
    {0x0C, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x0C, 0x00},
    // % (37)
    {0x00, 0x63, 0x33, 0x18, 0x0C, 0x66, 0x63, 0x00},
    // & (38)
    {0x1C, 0x36, 0x1C, 0x6E, 0x3B, 0x33, 0x6E, 0x00},
    // ' (39)
    {0x06, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00},
    // ( (40)
    {0x18, 0x0C, 0x06, 0x06, 0x06, 0x0C, 0x18, 0x00},
    // ) (41)
    {0x06, 0x0C, 0x18, 0x18, 0x18, 0x0C, 0x06, 0x00},
    // * (42)
    {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},
    // + (43)
    {0x00, 0x0C, 0x0C, 0x3F, 0x0C, 0x0C, 0x00, 0x00},
    // , (44)
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x06, 0x00},
    // - (45)
    {0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00},
    // . (46)
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00},
    // / (47)
    {0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x01, 0x00},
    // 0 (48)
    {0x3C, 0x66, 0x6E, 0x76, 0x66, 0x66, 0x3C, 0x00},
    // 1 (49)
    {0x18, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00},
    // 2 (50)
    {0x3C, 0x66, 0x30, 0x18, 0x0C, 0x66, 0x7E, 0x00},
    // 3 (51)
    {0x3C, 0x66, 0x30, 0x38, 0x30, 0x66, 0x3C, 0x00},
    // 4 (52)
    {0x38, 0x3C, 0x36, 0x33, 0x7F, 0x30, 0x78, 0x00},
    // 5 (53)
    {0x7E, 0x06, 0x3E, 0x60, 0x60, 0x66, 0x3C, 0x00},
    // 6 (54)
    {0x3C, 0x66, 0x06, 0x3E, 0x66, 0x66, 0x3C, 0x00},
    // 7 (55)
    {0x7E, 0x66, 0x30, 0x18, 0x18, 0x18, 0x18, 0x00},
    // 8 (56)
    {0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00},
    // 9 (57)
    {0x3C, 0x66, 0x66, 0x7C, 0x60, 0x66, 0x3C, 0x00},
    // : (58)
    {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x0C, 0x00},
    // ; (59)
    {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x06, 0x00},
    // < (60)
    {0x18, 0x0C, 0x06, 0x03, 0x06, 0x0C, 0x18, 0x00},
    // = (61)
    {0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00},
    // > (62)
    {0x06, 0x0C, 0x18, 0x30, 0x18, 0x0C, 0x06, 0x00},
    // ? (63)
    {0x3C, 0x66, 0x30, 0x18, 0x18, 0x00, 0x18, 0x00},
    // @ (64)
    {0x3C, 0x66, 0x73, 0x7B, 0x7B, 0x03, 0x1E, 0x00},
    // A (65)
    {0x18, 0x3C, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00},
    // B (66)
    {0x3F, 0x66, 0x66, 0x3E, 0x66, 0x66, 0x3F, 0x00},
    // C (67)
    {0x3C, 0x66, 0x03, 0x03, 0x03, 0x66, 0x3C, 0x00},
    // D (68)
    {0x1F, 0x36, 0x66, 0x66, 0x66, 0x36, 0x1F, 0x00},
    // E (69)
    {0x7F, 0x06, 0x06, 0x3E, 0x06, 0x06, 0x7F, 0x00},
    // F (70)
    {0x7F, 0x06, 0x06, 0x3E, 0x06, 0x06, 0x06, 0x00},
    // G (71)
    {0x3C, 0x66, 0x03, 0x73, 0x63, 0x66, 0x7C, 0x00},
    // H (72)
    {0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00},
    // I (73)
    {0x3C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00},
    // J (74)
    {0x78, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E, 0x00},
    // K (75)
    {0x67, 0x66, 0x36, 0x1E, 0x36, 0x66, 0x67, 0x00},
    // L (76)
    {0x0F, 0x06, 0x06, 0x06, 0x46, 0x66, 0x7F, 0x00},
    // M (77)
    {0x63, 0x77, 0x7F, 0x7F, 0x6B, 0x63, 0x63, 0x00},
    // N (78)
    {0x63, 0x67, 0x6F, 0x7B, 0x73, 0x63, 0x63, 0x00},
    // O (79)
    {0x1C, 0x36, 0x63, 0x63, 0x63, 0x36, 0x1C, 0x00},
    // P (80)
    {0x3F, 0x66, 0x66, 0x3E, 0x06, 0x06, 0x0F, 0x00},
    // Q (81)
    {0x1E, 0x33, 0x33, 0x33, 0x3B, 0x1E, 0x38, 0x00},
    // R (82)
    {0x3F, 0x66, 0x66, 0x3E, 0x36, 0x66, 0x67, 0x00},
    // S (83)
    {0x1E, 0x33, 0x07, 0x0E, 0x38, 0x33, 0x1E, 0x00},
    // T (84)
    {0x3F, 0x2D, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00},
    // U (85)
    {0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x3F, 0x00},
    // V (86)
    {0x33, 0x33, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00},
    // W (87)
    {0x63, 0x63, 0x63, 0x6B, 0x7F, 0x77, 0x63, 0x00},
    // X (88)
    {0x63, 0x63, 0x36, 0x1C, 0x1C, 0x36, 0x63, 0x00},
    // Y (89)
    {0x33, 0x33, 0x33, 0x1E, 0x0C, 0x0C, 0x1E, 0x00},
    // Z (90)
    {0x7F, 0x63, 0x31, 0x18, 0x4C, 0x66, 0x7F, 0x00}
};

// BootScreen类实现
BootScreen::BootScreen() {
    // 构造函数，无需特殊初始化
}

void BootScreen::calculateCenterPosition(int16_t& marioX, int16_t& textX, int16_t& textY) {
    // 全屏240x240布局
    // 马里奥精灵尺寸：13x16像素，3倍缩放后为39x48像素
    int16_t marioWidth = MARIO_IDLE_SIZE[0] * 3;   // 39像素
    int16_t marioHeight = MARIO_IDLE_SIZE[1] * 3;  // 48像素

    // "X 3" 文字尺寸：3个字符 * 8像素宽度 * 2倍缩放 = 48像素宽度，8像素高度 * 2倍缩放 = 16像素高度
    int16_t textWidth = 3 * 8 * 2;   // 48像素
    int16_t textHeight = 8 * 2;      // 16像素

    // 马里奥和文字之间的间距
    int16_t spacing = 16;

    // 计算整体宽度（马里奥 + 间距 + 文字）
    int16_t totalWidth = marioWidth + spacing + textWidth;

    // 计算起始X位置，使整体在240x240屏幕中居中
    int16_t startX = (240 - totalWidth) / 2;

    // 马里奥位置
    marioX = startX;
    int16_t marioY = (240 - marioHeight) / 2;  // 垂直居中

    // 文字位置（与马里奥垂直居中对齐）
    textX = startX + marioWidth + spacing;
    textY = marioY + (marioHeight - textHeight) / 2;

    USBSerial.print("Mario position: (");
    USBSerial.print(marioX);
    USBSerial.print(", ");
    USBSerial.print(marioY);
    USBSerial.print("), Text position: (");
    USBSerial.print(textX);
    USBSerial.print(", ");
    USBSerial.print(textY);
    USBSerial.println(")");
}

void BootScreen::drawInitialScreen() {
    // 全屏240x240绘制，填充纯黑色背景
    gfx->fillScreen(0x0000);  // 0x0000 = 纯黑色

    // 绘制顶部标题 "MARIO CLOCK" - 全屏居中
    drawTextDirect("MARIO CLOCK", 240/2 - (11 * 8 * 2)/2, 30, 0xFFFF, 2);  // 白色，2倍缩放

    // 计算马里奥位置
    int16_t marioX, textX, textY;
    calculateCenterPosition(marioX, textX, textY);

    // 绘制3倍缩放的马里奥精灵 - 直接绘制到全屏
    for (int j = 0; j < MARIO_IDLE_SIZE[1]; j++) {
        for (int i = 0; i < MARIO_IDLE_SIZE[0]; i++) {
            uint16_t color = MARIO_IDLE[j * MARIO_IDLE_SIZE[0] + i];

            // 跳过透明像素
            if (color == _MASK) continue;

            // 绘制3x3的像素块，直接到屏幕
            for (int sy = 0; sy < 3; sy++) {
                for (int sx = 0; sx < 3; sx++) {
                    gfx->drawPixel(marioX + (i * 3) + sx, (240 - MARIO_IDLE_SIZE[1] * 3) / 2 + (j * 3) + sy, color);
                }
            }
        }
    }
}

void BootScreen::clearCountdownArea(int16_t x, int16_t y) {
    // 精确清除倒计时文字区域，使用全屏坐标
    gfx->fillRect(x, y, 48, 16, 0x0000);  // 黑色矩形覆盖
}

void BootScreen::updateCountdown(int countdown) {
    // 计算倒计时文字位置
    int16_t marioX, textX, textY;
    calculateCenterPosition(marioX, textX, textY);

    // 直接绘制新的倒计时文字到全屏
    char countdownText[4];
    sprintf(countdownText, "X %d", countdown);
    drawTextDirect(countdownText, textX, textY, 0xFFFF, 2);  // 白色，2倍缩放
}

void BootScreen::drawChar(char c, int16_t x, int16_t y, uint16_t color) {
    drawChar(c, x, y, color, 2);  // 默认2倍缩放
}

void BootScreen::drawChar(char c, int16_t x, int16_t y, uint16_t color, int scale) {
    // 将字符转换为字体数组索引
    int index = c - 32;  // ASCII 32 是空格

    // 检查字符是否在支持范围内（我们定义了59个字符，从32到90）
    if (index < 0 || index >= 59) {
        index = 0;  // 使用空格代替不支持的字符
    }

    // 绘制8x8字符，指定缩放
    for (int row = 0; row < 8; row++) {
        uint8_t line = FONT_8X8[index][row];
        for (int col = 0; col < 8; col++) {
            uint16_t pixelColor;
            if (line & (0x01 << col)) {  // 修复：从左到右检查位
                pixelColor = color;  // 前景色
            } else {
                pixelColor = 0x0000;  // 背景色（黑色）
            }

            // 绘制scale x scale的像素块
            for (int sy = 0; sy < scale; sy++) {
                for (int sx = 0; sx < scale; sx++) {
                    drawPixelVirtual(x + (col * scale) + sx, y + (row * scale) + sy, pixelColor);
                }
            }
        }
    }
}

void BootScreen::drawText(const char* text, int16_t x, int16_t y, uint16_t color) {
    drawText(text, x, y, color, 2);  // 默认2倍缩放
}

void BootScreen::drawText(const char* text, int16_t x, int16_t y, uint16_t color, int scale) {
    int16_t currentX = x;

    for (int i = 0; text[i] != '\0'; i++) {
        drawChar(text[i], currentX, y, color, scale);
        currentX += 8 * scale;  // 每个字符宽度8像素 * 缩放倍数
    }
}

// 新增：直接绘制到全屏的文字方法
void BootScreen::drawTextDirect(const char* text, int16_t x, int16_t y, uint16_t color, int scale) {
    int16_t currentX = x;

    for (int i = 0; text[i] != '\0'; i++) {
        drawCharDirect(text[i], currentX, y, color, scale);
        currentX += 8 * scale;  // 每个字符宽度8像素 * 缩放倍数
    }
}

// 新增：直接绘制字符到全屏
void BootScreen::drawCharDirect(char c, int16_t x, int16_t y, uint16_t color, int scale) {
    // 将字符转换为字体数组索引
    int index = c - 32;  // ASCII 32 是空格

    // 检查字符是否在支持范围内（我们定义了59个字符，从32到90）
    if (index < 0 || index >= 59) {
        index = 0;  // 使用空格代替不支持的字符
    }

    // 绘制8x8字符，指定缩放，直接到屏幕
    for (int row = 0; row < 8; row++) {
        uint8_t line = FONT_8X8[index][row];
        for (int col = 0; col < 8; col++) {
            uint16_t pixelColor;
            if (line & (0x01 << col)) {  // 修复：从左到右检查位
                pixelColor = color;  // 前景色
            } else {
                pixelColor = 0x0000;  // 背景色（黑色）
            }

            // 绘制scale x scale的像素块，直接到屏幕
            for (int sy = 0; sy < scale; sy++) {
                for (int sx = 0; sx < scale; sx++) {
                    gfx->drawPixel(x + (col * scale) + sx, y + (row * scale) + sy, pixelColor);
                }
            }
        }
    }
}

void BootScreen::show() {
    USBSerial.println("=== Boot Screen Starting ===");

    // 首次绘制静态元素（标题和马里奥）
    drawInitialScreen();

    // 倒计时显示：3, 2, 1
    for (int countdown = 3; countdown >= 1; countdown--) {
        USBSerial.print("Countdown: ");
        USBSerial.println(countdown);

        // 只更新倒计时数字，减少闪烁
        updateCountdown(countdown);

        // 等待1秒
        delay(1000);
    }

    USBSerial.println("=== Boot Screen Finished ===");
}

// 静态成员变量，用于在静态函数中访问实例
static BootScreen* currentBootScreen = nullptr;

// 静态回调函数，用于状态更新
static void statusUpdateCallback(const char* status) {
    if (currentBootScreen != nullptr) {
        currentBootScreen->updateStatus(status);
    }
}

void BootScreen::showWithInit(void (*initCallback)(int countdown, bool* networkFailed, void (*statusCallback)(const char*))) {
    USBSerial.println("=== Boot Screen with Init Starting ===");

    // 设置当前实例指针
    currentBootScreen = this;

    // 首次绘制静态元素（标题和马里奥）
    drawInitialScreen();

    bool networkFailed = false;

    // 倒计时显示：3, 2, 1，同时执行初始化
    for (int countdown = 3; countdown >= 1; countdown--) {
        USBSerial.print("Countdown: ");
        USBSerial.println(countdown);

        // 更新倒计时数字
        updateCountdown(countdown);

        // 执行初始化回调，传递静态状态更新函数
        if (initCallback != nullptr) {
            initCallback(countdown, &networkFailed, statusUpdateCallback);
        }

        // 等待1秒
        delay(1000);
    }

    // 如果网络连接失败，显示错误提示
    if (networkFailed) {
        showNetworkError();
        delay(2000);  // 显示错误信息2秒
    }

    // 清除当前实例指针
    currentBootScreen = nullptr;

    USBSerial.println("=== Boot Screen with Init Finished ===");
}

// 新增：更新底部状态文字
void BootScreen::updateStatus(const char* status) {
    // 先清除之前的状态文字
    clearStatus();

    // 如果状态为空字符串，只清除不绘制
    if (status == nullptr || strlen(status) == 0) {
        return;
    }

    // 计算状态文字位置（底部居中，y=200）
    int textLength = strlen(status);
    int16_t statusX = (240 - textLength * 8 * 1) / 2;  // 1倍缩放，居中
    int16_t statusY = 200;

    // 绘制新的状态文字
    drawTextDirect(status, statusX, statusY, 0xFFFF, 1);  // 白色，1倍缩放

    USBSerial.print("Status: ");
    USBSerial.println(status);
}

// 新增：清除底部状态文字
void BootScreen::clearStatus() {
    // 清除底部状态区域（y=200, 高度16像素）
    gfx->fillRect(0, 200, 240, 16, 0x0000);  // 黑色矩形覆盖整个底部状态区域
}

void BootScreen::showNetworkError() {
    // 在屏幕底部显示网络连接失败提示 - 全屏240x240
    drawTextDirect("NETWORK CONNECTION FAILED", 240/2 - (23 * 8 * 1)/2, 200, 0xFFFF, 1);  // 白色，1倍缩放，底部位置
}
