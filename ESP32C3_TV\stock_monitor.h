#ifndef STOCK_MONITOR_H
#define STOCK_MONITOR_H

#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include "Arduino_GFX_Library.h"

// 股票数据结构
struct StockData {
    String code;           // 股票代码
    String name;           // 股票名称
    float currentPrice;    // 当前价格
    float changeAmount;    // 涨跌额
    float changePercent;   // 涨跌幅
    bool isValid;          // 数据是否有效
    unsigned long lastUpdate; // 最后更新时间
};

// 指数数据结构
struct IndexData {
    String name;           // 指数名称
    float currentValue;    // 当前点位
    float changeAmount;    // 涨跌点数
    float changePercent;   // 涨跌幅
    bool isValid;          // 数据是否有效
    unsigned long lastUpdate; // 最后更新时间
};

// 股票监控模块
class StockMonitor {
public:
    StockMonitor();
    ~StockMonitor();

    // 初始化和配置
    void init(String stockCodes[4]);
    void setStockCodes(String stockCodes[4]);
    void setUpdateInterval(unsigned long interval);
    
    // 主要功能
    void update();         // 更新股票数据
    void display();        // 显示股票信息
    bool isDataReady();    // 检查数据是否准备就绪
    void clearScreen();    // 清除屏幕
    void cleanup();        // 清理资源
    void resetFirstDisplay(); // 重置首次显示标志

    // 状态查询
    bool isMarketOpen();   // 检查市场是否开盘
    String getLastError(); // 获取最后的错误信息
    
    // 数据访问
    StockData getStockData(int index);
    IndexData getIndexData(int index);
    
private:
    // 股票数据
    StockData stocks[4];
    IndexData indices[2];  // 上证指数和深证成指
    
    // 配置参数
    String stockCodes[4];
    unsigned long updateInterval;
    unsigned long lastUpdateTime;
    String lastError;

    // 滚动错误提示相关
    int scrollOffset;
    unsigned long lastScrollTime;
    bool hasError;

    // 智能局部刷新：缓存上次显示的数据
    StockData lastDisplayedStocks[4];
    IndexData lastDisplayedIndices[2];
    String lastDisplayedError;
    bool isFirstDisplay;
    
    // 网络和API相关
    HTTPClient http;
    bool fetchStockData();
    bool fetchIndexData();
    bool parseStockResponse(String response, int validStockCount);
    bool parseSingleStockLine(String line, int index);
    bool parseIndexResponse(String response);
    bool parseSingleIndexLine(String line, int index);
    
    // 股票专用双缓冲
    uint16_t *stockFrameBuffer;
    bool useStockFrameBuffer;

    // 股票专用双缓冲函数
    bool initStockFrameBuffer();
    void renderStockFrameBuffer();
    void clearStockFrameBuffer();
    void drawPixelToStockBuffer(int16_t x, int16_t y, uint16_t color);
    void drawTextToStockBuffer(String text, int16_t x, int16_t y, uint16_t color, int size);

    // 智能局部刷新相关
    bool hasStockDataChanged(int index);
    bool hasIndexDataChanged(int index);
    bool hasErrorChanged();
    void updateDisplayCache();
    void clearTextArea(int16_t x, int16_t y, int16_t w, int16_t h);

    // 显示相关
    void drawStockInfo(int index, int16_t y);
    void drawIndexInfo(int index, int16_t y);
    void drawScrollingError();
    void updateScrollingError();
    uint16_t getChangeColor(float changePercent);
    
    // 工具函数
    String getStockAPI(String code);
    String getIndexAPI(int index);
    bool isValidStockCode(String code);
    String getExchangeName(String code);
    bool isMarketTime();
    
    // 常量定义
    static const unsigned long DEFAULT_UPDATE_INTERVAL = 60000; // 1分钟
    static const unsigned long MARKET_UPDATE_INTERVAL = 60000;  // 交易时间1分钟
    static const unsigned long AFTER_MARKET_UPDATE_INTERVAL = 1800000; // 非交易时间30分钟
    static const int MAX_RETRY_COUNT = 3;
    static const int HTTP_TIMEOUT = 10000; // 10秒超时
};

// 外部声明
extern Arduino_GFX *gfx;

#endif // STOCK_MONITOR_H
